package com.facishare.fmcg.provider.impl.rebate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fmcg.adapter.metadata.FieldDescribeAdapter;
import com.facishare.fmcg.adapter.metadata.dto.field.Add;
import com.facishare.fmcg.api.error.FmcgException;
import com.facishare.fmcg.api.service.rebate.IRebateService;
import com.facishare.fmcg.provider.abstraction.ServiceBase;
import com.facishare.fmcg.provider.business.abstraction.FieldBusiness;
import com.fmcg.framework.http.FundAccountProxy;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.PaasDescribeProxy;
import com.fmcg.framework.http.contract.fund.AddIncomeAuthorizationDetails;
import com.fmcg.framework.http.contract.paas.data.GetConfigValueByKey;
import com.fmcg.framework.http.contract.paas.data.PaasDataQuery;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeGet;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class RebateService extends ServiceBase implements IRebateService {

    @Resource
    private FieldBusiness fieldBusiness;
    @Resource
    private FieldDescribeAdapter fieldDescribeAdapter;
    @Resource
    private PaasDataProxy paasDataProxy;
    @Resource
    private FundAccountProxy fundAccountProxy;
    @Resource
    private PaasDescribeProxy paasDescribeProxy;

    @Override
    public void addField(Integer tenantId, String describeApiName, String fieldApiName, boolean isReadOnly, Boolean showInLayoutList) throws IOException {
        Add.Arg arg = fieldBusiness.buildFieldArg(tenantId, describeApiName, fieldApiName, isReadOnly, showInLayoutList);
        Add.Result result = fieldDescribeAdapter.create(tenantId, arg);
        if (result == null || !Objects.equals(0, result.getErrCode())) {
            log.info("add field error:{},{}", result == null ? "null" : result.getErrMessage(), JSON.toJSONString(arg));
        }
    }

    @Override
    public boolean isOpenRebate(Integer tenantId) {
        GetConfigValueByKey.Arg getConfigArg = new GetConfigValueByKey.Arg();
        getConfigArg.setKey("rebate");
        GetConfigValueByKey.Result configValueByKey = paasDataProxy.getConfigValueByKey(tenantId, -10000, getConfigArg);
        if (configValueByKey.getCode() != 0) {
            throw new FmcgException("查询是否开启返利失败", -1);
        }
        return Objects.equals(configValueByKey.getData().getValue(), "1");
    }

    public JSONObject getFAccountAuthorizations(Integer tenantId, String apiName) {
        PaasDataQuery.Arg queryArg = new PaasDataQuery.Arg();
        queryArg.setObjectApiName("FAccountAuthorizationObj");
        PaasDataQuery.QueryDTO queryDTO = new PaasDataQuery.QueryDTO();
        queryArg.setQuery(queryDTO);
        queryDTO.setLimit(1);
        queryDTO.setOffset(0);
        PaasDataQuery.FilterDTO filter = new PaasDataQuery.FilterDTO();
        filter.setFieldName("authorized_object_api_name");
        filter.setOperator("EQ");
        filter.setFieldValues(apiName);

        queryDTO.setFilters(Lists.newArrayList(filter));
        PaasDataQuery.Result result = paasDataProxy.query(tenantId, -10000, queryArg);
        if (result.getErrCode() != 0 || result.getResult().getDataList().size() == 0) {
            return new JSONObject();
        }
        return result.getResult().getDataList().get(0);
    }

    private List<JSONObject> getAuthorizationDetails(Integer tenantId, String masterId) {
        PaasDataQuery.Arg queryArg = new PaasDataQuery.Arg();
        queryArg.setObjectApiName("AuthorizationDetailObj");
        PaasDataQuery.QueryDTO queryDTO = new PaasDataQuery.QueryDTO();
        queryArg.setQuery(queryDTO);
        queryDTO.setLimit(2000);
        queryDTO.setOffset(0);
        PaasDataQuery.FilterDTO filter = new PaasDataQuery.FilterDTO();
        filter.setFieldName("faccount_authorization_id");
        filter.setOperator("EQ");
        filter.setFieldValues(masterId);

        queryDTO.setFilters(Lists.newArrayList(filter));
        PaasDataQuery.Result result = paasDataProxy.query(tenantId, -10000, queryArg);
        if (result.getErrCode() != 0 || result.getResult().getDataList().size() == 0) {
            return Lists.newArrayList();
        }
        return result.getResult().getDataList();
    }

    @Override
    public void syncRebateAccountToDealerCostAccount(Integer tenantId) {
        JSONObject rebateObj = getFAccountAuthorizations(tenantId, "RebateObj");
        if (rebateObj.size() == 0) {
            log.error("getFAccountAuthorizations for dealer cost is empty,tenantId:{}", tenantId);
            return;
        }
        String rebateId = (String) rebateObj.get("_id");
        List<JSONObject> rebateDetails = getAuthorizationDetails(tenantId, rebateId);

        for (JSONObject rebateDetail : rebateDetails) {
            String authorizeAccountId = rebateDetail.getString("authorize_account_id");
            AddIncomeAuthorizationDetails.Arg arg = new AddIncomeAuthorizationDetails.Arg();
            arg.setAuthorizedObjectApiName("TPMDealerActivityCostObj");

            AddIncomeAuthorizationDetails.NewAuthorizeAccountsArg authorizeAccountsArg = new AddIncomeAuthorizationDetails.NewAuthorizeAccountsArg();
            authorizeAccountsArg.setAuthorizeAccountId(authorizeAccountId);
            authorizeAccountsArg.setDefaultEntryAccount(false);
            arg.setNewAuthorizeAccounts(Lists.newArrayList(authorizeAccountsArg));

            AddIncomeAuthorizationDetails.Result result = addFundAccountToObj(tenantId, arg);
            if (result.getErrCode() != 0) {
                log.error("add fund account  to Rebate error,accountId:{},error message:{}", authorizeAccountId, result.getErrMessage());
            }
        }
    }

    @Override
    public AddIncomeAuthorizationDetails.Result addFundAccountToObj(Integer tenantId, AddIncomeAuthorizationDetails.Arg arg) {
        return fundAccountProxy.addIncomeAuthorizationDetails(tenantId, -10000, arg);
    }

    @Override
    public void checkRebateFiled(Integer tenantId) {
        try {
            if (isOpenRebate(tenantId)) {
                PaasDescribeGet.Result rebateObj = paasDescribeProxy.get(tenantId, -10000, "RebateObj");
                if (rebateObj.getCode() != 0) {
                    log.error("返利单对象不存在：{}", tenantId);
                    return;
                }
                JSONObject tpmActivityId = rebateObj.getData().getDescribe().getFields().get("tpm_activity_id");
                if (tpmActivityId == null) {
                    addField(tenantId, "RebateObj", "tpm_activity_id", true, false);
                }
                JSONObject tpmDealerActivityCostId = rebateObj.getData().getDescribe().getFields().get("tpm_dealer_activity_cost_id");
                if (tpmDealerActivityCostId == null) {
                    addField(tenantId, "RebateObj", "tpm_dealer_activity_cost_id", true, false);
                }
                //同步返利单的账号到经销商费用核销上
                syncRebateAccountToDealerCostAccount(tenantId);
            }
        } catch (Exception ex) {
            log.error("刷返利单字段失败:{},ex", tenantId, ex);
        }
    }
}
