package com.facishare.fmcg.provider.business.model;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 *
 */
@Data
@ToString
@Builder
public class AddFieldBO {

    private String describeApiName;

    private List<FiledBO> filedBOs;

    @Data
    @ToString
    @Builder
    public static class FiledBO {
        private String fieldApiName;
        private boolean isReadOnly;
        private boolean isShowInLayout = true;
    }
}
