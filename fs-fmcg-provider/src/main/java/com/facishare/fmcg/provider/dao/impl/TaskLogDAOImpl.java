package com.facishare.fmcg.provider.dao.impl;

import com.facishare.fmcg.provider.dao.abstraction.DaoBase;
import com.facishare.fmcg.provider.dao.abstraction.TaskLogDAO;
import com.facishare.fmcg.provider.task.po.TaskLogPO;
import com.facishare.fmcg.provider.task.po.TaskPO;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Author: linmj
 * Date: 2023/5/31 16:04
 */

@Repository
public class TaskLogDAOImpl extends DaoBase implements TaskLogDAO {


    @Override
    public String save(TaskLogPO taskLogPO) {
        return dbContext.save(taskLogPO).getId().toString();
    }

    @Override
    public List<TaskLogPO> queryAllTaskLogByTaskId(String tenantId, String taskId) {
        Query<TaskLogPO> query = dbContext.createQuery(TaskLogPO.class);
        query.field(TaskLogPO.F_TASK_ID).equal(taskId);
        query.field(TaskLogPO.F_IS_DELETED).equal(false);
        return query.asList();
    }
}
