package com.facishare.fmcg.provider.impl.auth;

import com.facishare.fmcg.provider.abstraction.ServiceBase;
import com.facishare.paas.auth.common.exception.AuthException;
import com.facishare.paas.auth.model.AuthContext;
import com.facishare.paas.auth.model.RolePojo;
import com.facishare.paas.auth.model.params.request.AddRoleGroupArg;
import com.facishare.paas.auth.model.params.request.CreateRoleArg;
import com.fxiaoke.paas.auth.factory.RoleClient;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


@SuppressWarnings("Duplicates")
@Slf4j
@Component("tpmAuthService")
public class TPMAuthServiceImpl extends ServiceBase {

    private static final String DEFAULT_ACCESS_ROLE_GROUP = "fmcgRoleGroup";
    @Resource
    private RoleClient roleClient;

    public void createRole(String tenantId, String roleName, String licenseCode, String roleCode) {
        addRoleGroup(tenantId, "快消应用许可", Lists.newArrayList(roleCode));

        CreateRoleArg arg = new CreateRoleArg();
        arg.setRoleType(1);
        arg.setLicenseCode(licenseCode);
        arg.setRoleCode(roleCode);
        arg.setRoleName(roleName);
        arg.setDescription(roleName);
        arg.setAuthContext(initAuthContextDefault(tenantId));
        arg.setGroupCode(DEFAULT_ACCESS_ROLE_GROUP);
        roleClient.createRole(arg);
    }

    public void updateRole(String tenantId, String roleName, String licenseCode, String roleCode) {
        RolePojo rolePojo = new RolePojo();
        rolePojo.setRoleType(1);
        rolePojo.setLicenseCode(licenseCode);
        rolePojo.setRoleCode(roleCode);
        rolePojo.setRoleName(roleName);
        rolePojo.setDescription(roleName);
        rolePojo.setGroupCode(DEFAULT_ACCESS_ROLE_GROUP);
        roleClient.updateRole(initAuthContextDefault(tenantId), rolePojo);
    }

    public void addRoleGroup(String tenantId, String roleGroupName, List<String> roleCodes) {
        AddRoleGroupArg addRoleGroupArg = new AddRoleGroupArg();
        addRoleGroupArg.setId(DEFAULT_ACCESS_ROLE_GROUP);
        addRoleGroupArg.setRoleGroupName(roleGroupName);
        addRoleGroupArg.setRoleGroupDescription(roleGroupName);
      //  addRoleGroupArg.setRoleCodes(roleCodes);
        addRoleGroupArg.setRoleGroupType(0);
        addRoleGroupArg.setAuthContext(AuthContext.builder().tenantId(tenantId).userId("-10000").appId("CRM").build());
        try {
            roleClient.addRoleGroup(addRoleGroupArg);
        } catch (Exception e) {
            log.info("addRoleGroup error tenatId {} ,roleId {}", tenantId, DEFAULT_ACCESS_ROLE_GROUP, e);
        }
    }

    AuthContext initAuthContextDefault(String tenantId) {
        return AuthContext.builder().appId("CRM").tenantId(tenantId).userId("-10000").build();
    }

}
