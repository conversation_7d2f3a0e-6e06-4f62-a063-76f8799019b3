package com.facishare.fmcg.provider.impl.card;

import com.facishare.cep.plugin.exception.BizException;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.fmcg.adapter.card.ScanAdapter;
import com.facishare.fmcg.adapter.card.StoneAdapter;
import com.facishare.fmcg.api.dto.card.RecognizeCardArg;
import com.facishare.fmcg.api.dto.card.RecognizeCardResult;
import com.github.autoconf.ConfigFactory;
import com.github.rholder.retry.*;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class RecognizeService implements IRecognizeService {

    private static final String DEFAULT_URL = "http://bcr2.intsig.net/BCRService/BCR_VCF2?user=<EMAIL>&pass=BG3K78FNQCQJAMBM&json=1&lang=7&size=";
    public static final int RETRY_TIMES = 3;
    public static final Retryer<String> RETRY_EXECUTOR = RetryerBuilder.<String>newBuilder().retryIfResult(Strings::isNullOrEmpty).withWaitStrategy(WaitStrategies.fixedWait(2, TimeUnit.SECONDS)).withStopStrategy(StopStrategies.stopAfterAttempt(RETRY_TIMES)).build();

    private String proxyHost = "************";
    private Integer proxyPort = 9999;
    private String bcrUrl = "";

    @PostConstruct
    void init() {
        ConfigFactory.getConfig("RecognizecardService", config -> {
            bcrUrl = config.get("recognizecard.bcr.url", DEFAULT_URL);
            proxyHost = config.get("proxyHost", proxyHost);
            proxyPort = config.getInt("proxyPort", proxyPort);
        });
    }

    @Resource
    private StoneAdapter stoneAdapter;

    public RecognizeCardResult recognizeCard(UserInfo user, RecognizeCardArg arg) {
        log.info("recognize card parameters - tenant account : {}, file path : {}", user.getEnterpriseAccount(), arg.getFilePath());

        if (Objects.isNull(user.getEnterpriseId())) {
            throw new BizException("企业上下文信息异常。", 40009);
        }

        String recognizeCardResult;
        try {
            recognizeCardResult = RETRY_EXECUTOR.call(() -> scan(user.getEnterpriseId().toString(), user.getEnterpriseAccount(), arg.getFilePath()));
        } catch (Exception ex) {
            if (ex instanceof ExecutionException || ex instanceof RetryException) {
                log.info("scan card cause execution exception.", ex);
            } else {
                log.error("scan card cause unknown exception.", ex);
            }
            throw new BizException("名片信息扫描失败，请稍后重试。", 50004);
        }

        if (Strings.isNullOrEmpty(recognizeCardResult)) {
            throw new BizException("名片信息扫描失败，请稍后重试。", 50005);
        }

        log.info("recognize card result : {}", recognizeCardResult);

        return new RecognizeCardResult(recognizeCardResult);
    }

    private String scan(String tenantId, String tenantAccount, String path) {
        InputStream inputStream;
        try {
            inputStream = stoneAdapter.downloadStream(tenantId, tenantAccount, -10000, "recognizecard", path);
        } catch (IOException ex) {
            log.error("download file stream error.", ex);
            return "";
        }
        if (inputStream == null) {
            log.info("file stream is null.");
            return "";
        }

        if (bcrUrl == null || bcrUrl.isEmpty() || bcrUrl.trim().isEmpty()) {
            log.info("bcr url is null or empty.");
            return "";
        }

        log.info("recognize card - url : {}, proxy host : {}, proxy port : {} ", bcrUrl, proxyHost, proxyPort);
        return ScanAdapter.uploadFile(tenantAccount, bcrUrl, proxyHost, proxyPort, "upfile", path, inputStream);
    }
}
