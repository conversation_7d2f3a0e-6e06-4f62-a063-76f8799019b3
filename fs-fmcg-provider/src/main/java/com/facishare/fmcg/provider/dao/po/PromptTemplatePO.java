package com.facishare.fmcg.provider.dao.po;

import lombok.Data;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.util.List;
import java.util.Map;

/**
 * 提示词模板持久化对象
 * 
 * <AUTHOR>
 * @date 2023/11/01
 */
@Data
@ToString
@Entity(value = "fmcg_prompt_template", noClassnameStored = true)
public class PromptTemplatePO {

    public static final String F_TENANT_ID = "TI";
    public static final String F_NAME = "N";
    public static final String F_CODE = "C";
    public static final String F_PROMPT_TEXT = "PT";
    public static final String F_MODEL_TYPE = "MT";
    public static final String F_INPUT_PARAMETERS = "IP";
    public static final String F_OUTPUT_PARAMETERS = "OP";
    public static final String F_CREATE_TIME = "CT";
    public static final String F_CREATOR = "CR";
    public static final String F_LAST_UPDATE_TIME = "LUT";
    public static final String F_LAST_UPDATER = "LU";
    public static final String F_DELETED = "DE";
    public static final String F_DELETE_TIME = "DT";

    @Id
    private ObjectId id;

    @Property(F_TENANT_ID)
    private Integer tenantId;

    @Property(F_NAME)
    private String name;

    @Property(F_CODE)
    private String code;

    @Property(F_PROMPT_TEXT)
    private String promptText;

    @Property(F_MODEL_TYPE)
    private String modelType;

    @Embedded(F_INPUT_PARAMETERS)
    private List<ParameterDefinition> inputParameters;

    @Embedded(F_OUTPUT_PARAMETERS)
    private List<ParameterDefinition> outputParameters;

    @Property(F_CREATE_TIME)
    private Long createTime;

    @Property(F_CREATOR)
    private Integer creator;

    @Property(F_LAST_UPDATE_TIME)
    private Long lastUpdateTime;

    @Property(F_LAST_UPDATER)
    private Integer lastUpdater;

    @Property(F_DELETED)
    private Boolean deleted = false;

    @Property(F_DELETE_TIME)
    private Long deleteTime;

    /**
     * 参数定义
     */
    @Data
    @ToString
    public static class ParameterDefinition {
        public static final String F_NAME = "N";
        public static final String F_TYPE = "T";
        public static final String F_DESCRIPTION = "D";
        public static final String F_REQUIRED = "R";
        public static final String F_DEFAULT_VALUE = "DV";
        public static final String F_EXAMPLE = "E";
        public static final String F_EXTRA_PROPERTIES = "EP";
        public static final String F_SHOW_IN_RULE = "SUR";

        @Property(F_NAME)
        private String name;
        
        @Property(F_TYPE)
        private String type;
        
        @Property(F_DESCRIPTION)
        private String description;
        
        @Property(F_REQUIRED)
        private Boolean required;

        @Property(F_SHOW_IN_RULE)
        private Boolean showInRule;
        
        @Property(F_DEFAULT_VALUE)
        private Object defaultValue;
        
        @Property(F_EXAMPLE)
        private Object example;
        
        @Property(F_EXTRA_PROPERTIES)
        private Map<String, Object> extraProperties;
    }
}