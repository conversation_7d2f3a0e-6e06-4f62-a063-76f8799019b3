package com.facishare.fmcg.provider.license.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fmcg.adapter.exception.AdapterException;
import com.facishare.fmcg.adapter.metadata.DataAdapter;
import com.facishare.fmcg.adapter.metadata.DescribeAdapter;
import com.facishare.fmcg.adapter.metadata.FieldDescribeAdapter;
import com.facishare.fmcg.adapter.metadata.dto.data.QueryLayout;
import com.facishare.fmcg.adapter.metadata.dto.describe.Create;
import com.facishare.fmcg.adapter.metadata.dto.field.Add;
import com.facishare.fmcg.provider.license.ModuleLicenseHandlerBase;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@SuppressWarnings("Duplicates")
@Component("coinLicenseHandler")
public class CoinLicenseHandler extends ModuleLicenseHandlerBase {


    private static final String[] API_NAMES = new String[5];
    private static final String DESCRIBE_CONFIG_ROOT = "fs-fmcg-dds-object-describe";
    private static final Set<String> SYSTEM_FIELDS = new HashSet<>();
    private static final Logger log = LoggerFactory.getLogger(CoinLicenseHandler.class);

    static {
        API_NAMES[0] = "PointsRewardDetailObj";
        API_NAMES[1] = "LedgerObj";
        API_NAMES[2] = "LedgerDetailObj";
        API_NAMES[3] = "CoinAccountObj";
        API_NAMES[4] = "CoinAccountDetailObj";

        SYSTEM_FIELDS.add("tenant_id");
        SYSTEM_FIELDS.add("name");
        SYSTEM_FIELDS.add("owner");
        SYSTEM_FIELDS.add("lock_status");
        SYSTEM_FIELDS.add("life_status");
        SYSTEM_FIELDS.add("record_type");
        SYSTEM_FIELDS.add("created_by");
        SYSTEM_FIELDS.add("create_time");
        SYSTEM_FIELDS.add("last_modified_by");
        SYSTEM_FIELDS.add("last_modified_time");
        SYSTEM_FIELDS.add("extend_obj_data_id");
        SYSTEM_FIELDS.add("package");
        SYSTEM_FIELDS.add("object_describe_id");
        SYSTEM_FIELDS.add("object_describe_api_name");
        SYSTEM_FIELDS.add("version");
        SYSTEM_FIELDS.add("lock_user");
        SYSTEM_FIELDS.add("lock_rule");
        SYSTEM_FIELDS.add("life_status_before_invalid");
        SYSTEM_FIELDS.add("is_deleted");
        SYSTEM_FIELDS.add("out_tenant_id");
        SYSTEM_FIELDS.add("out_owner");
        SYSTEM_FIELDS.add("data_own_department");
        SYSTEM_FIELDS.add("active_status");
        SYSTEM_FIELDS.add("relevant_team");
        SYSTEM_FIELDS.add("_id");
        SYSTEM_FIELDS.add("owner_department");
    }

    @Resource
    private DescribeAdapter describeAdapter;
    @Resource
    private FieldDescribeAdapter fieldDescribeAdapter;
    @Resource
    private DataAdapter dataAdapter;

    @Override
    public void active(int tenantId, String tenantAccount, int userId) {

        for (String apiName : API_NAMES) {
            try {
                Create.Arg arg = buildDescribeArg(apiName);
                if (arg != null) {

                    log.info("[coin license] create arg : {}", JSON.toJSONString(arg));

                    Create.Result result = describeAdapter.create(tenantId, buildDescribeArg(apiName));

                    log.info("[coin license] create result : {}", JSON.toJSONString(result));
                }
            } catch (AdapterException e) {
                log.error("active coin package fail when create describe object", e);
            }
        }

        fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "CoinAccountDetailObj", "Coin.remarks__c"));
        fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "CoinAccountDetailObj", "Coin.subject_three__c"));
        fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "CoinAccountDetailObj", "Coin.subject_two__c"));
        fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "CoinAccountDetailObj", "Coin.subject_one__c"));

        fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "LedgerDetailObj", "Coin.remarks__c"));
        fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "LedgerDetailObj", "Coin.subject_three__c"));
        fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "LedgerDetailObj", "Coin.subject_two__c"));
        fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "LedgerDetailObj", "Coin.subject_one__c"));

        fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "LedgerObj", "Coin.dealer_info__c"));
        fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "AccountObj", "AccountObj.dealer_owner__c"));
        fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "CoinAccountDetailObj", "Coin.work_time__c"));
        fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "PointsRewardDetailObj", "PointsRewardDetailObj.integral_status"));
        fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "PointsRewardDetailObj", "PointsRewardDetailObj.enable_time"));
        fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "PointsRewardDetailObj", "PointsRewardDetailObj.disable_time"));
        fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "PointsRewardDetailObj", "PointsRewardDetailObj.biz_data_id"));
        fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "PointsRewardDetailObj", "PointsRewardDetailObj.consume_status"));
        fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "PointsRewardDetailObj", "PointsRewardDetailObj.consumed_amount"));
        try {
            fieldDescribeAdapter.create(tenantId, buildFieldArg(tenantId, "PointsRewardDetailObj", "action_time", Lists.newArrayList("默认"), true));
        } catch (IOException e) {
            log.info("add field fail.", e);
        }
    }

    @Override
    public String formatOwnerCode(int tenantId, String tenantAccount, int userId) {
        return "TENANT." + tenantId;
    }

    @Override
    public String getAppCode() {
        return "FMCG.COIN";
    }

    private Create.Arg buildDescribeArg(String apiName) {
        IChangeableConfig config = ConfigFactory.getConfig(DESCRIBE_CONFIG_ROOT);
        String configJson = config.get(apiName);

        if (Strings.isNullOrEmpty(configJson)) {
            return null;
        }

        Create.Arg arg = new Create.Arg();

        arg.setActive(true);
        arg.setIncludeLayout(true);

        JSONObject describe = JSON.parseObject(configJson);
        arg.setJsonData(describe.toJSONString());

        JSONObject layOut = JSON.parseObject(config.get("common.layout"));
        layOut.put("ref_object_api_name", apiName);
        layOut.put("api_name", apiName + "_kx_layout__c");
        JSONArray components = layOut.getJSONArray("components");
        JSONObject firstComponent = components.getJSONObject(0);
        JSONArray fieldSection = firstComponent.getJSONArray("field_section");
        JSONObject firstFieldSection = fieldSection.getJSONObject(0);
        JSONArray formFields = firstFieldSection.getJSONArray("form_fields");
        JSONObject fields = describe.getJSONObject("fields");
        fields.keySet().forEach(key ->
        {
            if (!SYSTEM_FIELDS.contains(key)) {
                JSONObject field = fields.getJSONObject(key);
                JSONObject formField = new JSONObject();
                if ("quote".equals(field.getString("type"))) {
                    formField.put("is_readonly", true);
                } else {
                    formField.put("is_readonly", false);
                }
                formField.put("is_required", field.getBoolean("is_required"));
                formField.put("render_type", field.getString("type"));
                formField.put("field_name", field.getString("api_name"));
                formFields.add(formField);
            }
        });

        arg.setJsonLayout(layOut.toJSONString());
        JSONObject listLayOut = JSON.parseObject(config.get("common.list_layout"));
        listLayOut.put("ref_object_api_name", apiName);
        listLayOut.put("api_name", apiName + "_list_kx_layout__c");
        arg.setJsonListLayout(listLayOut.toJSONString());

        arg.setLayoutType("detail");
        return arg;
    }

    private Add.Arg buildFieldArg(Integer tenantId, String describeApiName, String key) {
        Add.Arg arg = new Add.Arg();
        IChangeableConfig config = ConfigFactory.getConfig(DESCRIBE_CONFIG_ROOT);

        arg.setDescribeAPIName(describeApiName);
        arg.setGroupFields("[]");

        JSONObject field = JSON.parseObject(config.get(key));
        field.put("describe_api_name", describeApiName);

        arg.setFieldDescribe(field.toJSONString());

        JSONObject layOutDto = new JSONObject();
        layOutDto.put("is_show", true);
        layOutDto.put("is_required", field.getBoolean("is_required"));
        if (field.getString("type").equals("quote")) {
            layOutDto.put("is_readonly", true);
        } else {
            layOutDto.put("is_readonly", false);
        }
        layOutDto.put("render_type", field.getString("type"));
        layOutDto.put("api_name", queryDefaultLayout(tenantId, describeApiName));
        layOutDto.put("is_default", true);
        layOutDto.put("label", field.getString("label"));

        JSONArray layOutList = new JSONArray(1);
        layOutList.add(layOutDto);
        arg.setLayoutList(layOutList.toJSONString());
        return arg;
    }

    private Add.Arg buildFieldArg(Integer tenantId, String describeApiName, String fieldApiName, List<String> layoutName, boolean isReadOnly) throws IOException {
        Add.Arg arg = new Add.Arg();
        String fileName = describeApiName + "." + fieldApiName + ".json";
        arg.setDescribeAPIName(describeApiName);
        arg.setGroupFields("[]");
        JSONObject fieldDes = JSON.parseObject(loadFieldFile(fileName));
        arg.setFieldDescribe(fieldDes.toJSONString());
        List<JSONObject> layoutList = queryLayoutList(tenantId, describeApiName);
        List<JSONObject> fieldLayout = Lists.newArrayList();
        for (JSONObject completeLayout : layoutList) {
            if (!"detail".equals(completeLayout.getString("layout_type")))
                continue;
            JSONObject tempLayout = new JSONObject();
            fieldLayout.add(tempLayout);
            String label = completeLayout.getString("display_name");
            tempLayout.put("api_name", completeLayout.getString("api_name"));
            tempLayout.put("label", label);
            tempLayout.put("layout_type", "detail");
            tempLayout.put("render_type", fieldDes.getString("type"));
            tempLayout.put("is_show", layoutName.stream().anyMatch(v -> label != null && label.contains(v)));
            tempLayout.put("is_required", false);
            tempLayout.put("is_readonly", isReadOnly);
        }
        arg.setLayoutList(fieldLayout.toString());
        return arg;
    }

    private String queryDefaultLayout(Integer tenantId, String apiName) {
        QueryLayout.Arg arg = new QueryLayout.Arg();
        arg.setObjectDescribeApiName(apiName);
        try {
            QueryLayout.Result r = dataAdapter.queryLayout(tenantId, arg);
            if (r.getLayouts() != null && !r.getLayouts().isEmpty()) {
                for (JSONObject v : r.getLayouts()) {
                    if ("默认布局".equals(v.getString("display_name"))) {
                        return v.getString("api_name");
                    }
                }
            }
        } catch (Exception e) {
            log.error("get layout from {}.{} err", tenantId, apiName);
            return apiName;
        }
        return apiName;
    }

    private String loadFieldFile(String fileName) throws IOException {
        File file = ResourceUtils.getFile(String.format("classpath:tpm/field/%s", fileName));
        return new String(Files.readAllBytes(file.toPath()));
    }

    private List<JSONObject> queryLayoutList(Integer tenantId, String apiName) {
        QueryLayout.Arg arg = new QueryLayout.Arg();
        arg.setObjectDescribeApiName(apiName);
        try {
            QueryLayout.Result r = dataAdapter.queryLayout(tenantId, arg);
            if (r.getLayouts() != null && !r.getLayouts().isEmpty()) {

                return r.getLayouts();
            }
        } catch (Exception e) {
            log.error("get layout from {}.{} err", tenantId, apiName);
        }
        return new ArrayList<>();
    }
}
