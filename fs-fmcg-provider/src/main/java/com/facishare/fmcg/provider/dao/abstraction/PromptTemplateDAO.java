package com.facishare.fmcg.provider.dao.abstraction;

import com.facishare.fmcg.provider.dao.po.PromptTemplatePO;

import java.util.List;

/**
 * 提示词模板DAO接口
 *
 * <AUTHOR>
 * @date 2023/11/01
 */
public interface PromptTemplateDAO {

    /**
     * 保存提示词模板
     *
     * @param po 提示词模板PO
     * @return 模板ID
     */
    String save(PromptTemplatePO po);

    /**
     * 根据code获取提示词模板
     *
     * @param id 模板ID
     * @return 提示词模板PO
     */
    PromptTemplatePO getByCode(Integer tenantId, String code);

    /**
     * 根据租户ID获取提示词模板列表
     *
     * @param tenantId 租户ID
     * @return 提示词模板列表
     */
    List<PromptTemplatePO> getByTenantId(Integer tenantId);

    /**
     * 根据租户ID和模型类型获取提示词模板列表
     *
     * @param tenantId  租户ID
     * @param modelType 模型类型
     * @return 提示词模板列表
     */
    List<PromptTemplatePO> getByTenantIdAndModelType(Integer tenantId, String modelType);

    /**
     * 更新提示词模板
     *
     * @param po 提示词模板PO
     */
    void update(PromptTemplatePO po);

    /**
     * 逻辑删除提示词模板
     *
     * @param id 模板ID
     */
    void delete(String id);
}