package com.facishare.fmcg.provider.dao.po;

import com.facishare.fmcg.api.dto.custom.QueryDevicePhotoDetail;
import com.facishare.fmcg.provider.dao.po.entities.LocationEntity;
import lombok.Data;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Description
 * @date 2022-03-08 18:00
 **/
@Data
@ToString
@Entity(value = "fmcg_device_photo_detail", noClassnameStored = true)
public class DevicePhotoDetailPO {

    public static final String F_TENANT_ID = "TI";

    public static final String F_CODE = "CD";
    public static final String F_COUNT = "CO";
    public static final String F_PRODUCT_NAME = "PN";
    public static final String F_MASTER_ID = "MI";
    public static final String F_LOCATION = "LO";

    public static final String F_APPEAL_COUNT = "ACO";


    public static final String F_CREATE_TIME = "CT";
    public static final String F_CREATOR = "CTO";
    public static final String F_DELETED_TIME = "DET";
    public static final String F_DELETED = "DE";

    public static final String F_LAST_UPDATER = "LU";
    public static final String F_LAST_UPDATE_TIME = "LUT";

    @Id
    private ObjectId id;

    @Property(F_TENANT_ID)
    private Integer tenantId;

    @Property(F_CODE)
    private String code;

    /**
     * ai识别数量
     */
    @Property(F_COUNT)
    private Long count;

    /**
     * 申诉数量
     */
    @Property(F_APPEAL_COUNT)
    private Long appealCount;

    @Property(F_PRODUCT_NAME)
    private String productName;

    @Property(F_MASTER_ID)
    private String masterId;


    @Embedded(F_LOCATION)
    private List<LocationEntity> location;


    @Property(F_CREATOR)
    private Integer creator;

    @Property(F_CREATE_TIME)
    private Long createTime;

    @Property(F_DELETED_TIME)
    private Date deleteTime;


    /**
     * 是否已经删除
     */
    @Property(F_DELETED)
    private Boolean deleted;

    @Property(F_LAST_UPDATER)
    private Integer lastUpdater;

    @Property(F_LAST_UPDATE_TIME)
    private Long lastUpdateTime;

    public static QueryDevicePhotoDetail.Detail toVO(DevicePhotoDetailPO po) {
        QueryDevicePhotoDetail.Detail detail = new QueryDevicePhotoDetail.Detail();
        //手工申诉数量没有或者为0 取AI识别数量,是否改动过数量标志位置为false
        if (po.getAppealCount() == null || po.getAppealCount() == 0) {
            detail.setCount(po.getCount());
            detail.setIsChangedCount(false);
        } else {
            detail.setCount(po.getAppealCount());
            detail.setIsChangedCount(true);
        }

        //手工添加的直接高亮
        if (po.getCount() == null || po.getCount() == 0) {
            detail.setIsChangedCount(true);
        } else if (po.getAppealCount() != null && po.getAppealCount().equals(po.getCount())) {
            //申诉前跟申诉后数量都不曾改变
            detail.setIsChangedCount(false);
        }
        detail.setDetailId(po.getId().toString());
        detail.setCode(po.getCode());
        detail.setName(po.getProductName());
        return detail;
    }
}
