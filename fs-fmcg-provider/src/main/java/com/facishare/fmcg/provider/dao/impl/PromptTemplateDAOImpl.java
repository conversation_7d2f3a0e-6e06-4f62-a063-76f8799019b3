package com.facishare.fmcg.provider.dao.impl;

import com.facishare.fmcg.provider.dao.abstraction.DaoBase;
import com.facishare.fmcg.provider.dao.abstraction.PromptTemplateDAO;
import com.facishare.fmcg.provider.dao.po.PromptTemplatePO;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 提示词模板DAO实现类
 * 
 * <AUTHOR>
 * @date 2023/11/01
 */
@Repository
public class PromptTemplateDAOImpl extends DaoBase implements PromptTemplateDAO {

    @Override
    public String save(PromptTemplatePO po) {
        po.setCreateTime(System.currentTimeMillis());
        po.setLastUpdateTime(System.currentTimeMillis());
        po.setDeleted(false);
        return dbContext.save(po).getId().toString();
    }

    @Override
    public PromptTemplatePO getByCode(Integer tenantId, String code) {
        Query<PromptTemplatePO> query = dbContext.createQuery(PromptTemplatePO.class);
        query.field(PromptTemplatePO.F_TENANT_ID).equal(tenantId);
        query.field(PromptTemplatePO.F_CODE).equal(code);
        query.field(PromptTemplatePO.F_DELETED).equal(false);
        return query.get();
    }

    @Override
    public List<PromptTemplatePO> getByTenantId(Integer tenantId) {
        Query<PromptTemplatePO> query = dbContext.createQuery(PromptTemplatePO.class);
        query.field(PromptTemplatePO.F_TENANT_ID).equal(tenantId);
        query.field(PromptTemplatePO.F_DELETED).equal(false);
        return query.asList();
    }

    @Override
    public List<PromptTemplatePO> getByTenantIdAndModelType(Integer tenantId, String modelType) {
        Query<PromptTemplatePO> query = dbContext.createQuery(PromptTemplatePO.class);
        query.field(PromptTemplatePO.F_TENANT_ID).equal(tenantId);
        query.field(PromptTemplatePO.F_MODEL_TYPE).equal(modelType);
        query.field(PromptTemplatePO.F_DELETED).equal(false);
        return query.asList();
    }

    @Override
    public void update(PromptTemplatePO po) {
        Query<PromptTemplatePO> query = dbContext.createQuery(PromptTemplatePO.class);
        query.field("_id").equal(po.getId());
        
        po.setLastUpdateTime(System.currentTimeMillis());
        ObjectId id = po.getId();
        po.setId(null);
        
        dbContext.updateFirst(query, po, false);
        po.setId(id);
    }

    @Override
    public void delete(String id) {
        Query<PromptTemplatePO> query = dbContext.createQuery(PromptTemplatePO.class);
        query.field("_id").equal(new ObjectId(id));
        
        PromptTemplatePO po = new PromptTemplatePO();
        po.setDeleted(true);
        po.setDeleteTime(System.currentTimeMillis());
        
        dbContext.updateFirst(query, po, false);
    }
}