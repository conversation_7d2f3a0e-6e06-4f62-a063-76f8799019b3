package com.facishare.fmcg.provider.business;

import com.facishare.fmcg.provider.business.abstraction.AccountBusiness;
import com.facishare.fmcg.provider.business.model.AccountBO;
import com.facishare.fmcg.provider.business.model.AccountDetailBO;
import com.facishare.fmcg.provider.business.model.BizCallNumberBO;
import com.facishare.fmcg.provider.business.model.PriceBO;
import com.facishare.fmcg.provider.dao.abstraction.AccountDAO;
import com.facishare.fmcg.provider.dao.abstraction.AccountDetailDAO;
import com.facishare.fmcg.provider.dao.abstraction.BizCallNumberDAO;
import com.facishare.fmcg.provider.dao.abstraction.PriceDAO;
import com.facishare.fmcg.provider.dao.po.AccountDetailPO;
import com.facishare.fmcg.provider.dao.po.AccountPO;
import com.facishare.fmcg.provider.dao.po.BizCallNumberPO;
import com.facishare.fmcg.provider.dao.po.PricePO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/7/16 下午5:29
 */
@Component
public class AccountBusinessImpl implements AccountBusiness {

    @Resource
    private AccountDetailDAO accountDetailDAO;

    @Resource
    private AccountDAO accountDAO;

    @Resource
    private PriceDAO priceDAO;

    @Resource
    private BizCallNumberDAO bizCallNumberDAO;


    @Override
    public void init(Integer tenantId, String biz,String modelId,Double unitPrice) {
        AccountPO accountPO = new AccountPO();
        accountPO.setTenantId(tenantId);
        if(accountDAO.query(tenantId).size()==0)
            accountDAO.insert(accountPO);
        BizCallNumberPO bizCallNumberPO = new BizCallNumberPO();
        bizCallNumberPO.setTenantId(tenantId);
        bizCallNumberPO.setBiz(biz);
        if(bizCallNumberDAO.query(tenantId,biz)==null)
             bizCallNumberDAO.save(bizCallNumberPO);
        if(modelId!=null){
            PricePO pricePO = new PricePO();
            pricePO.setModelId(modelId);
            pricePO.setUnitPrice(unitPrice==null?0.01:unitPrice);
            pricePO.setTenantId(tenantId);
            priceDAO.insert(pricePO);
        }
    }

    @Override
    public AccountBO addAccount(AccountBO accountBO) {
        AccountPO po = accountBO.transToPO();
        accountDAO.insert(po);
        BeanUtils.copyProperties(po,accountBO);
        return accountBO;
    }

    @Override
    public AccountDetailBO addAccountDetail(AccountDetailBO accountDetailBO) {
        AccountDetailPO po = accountDetailBO.transToPO();
        accountDetailDAO.insert(po);
        BeanUtils.copyProperties(po,accountDetailBO);
        accountDAO.riseBalance(accountDetailBO.getAccountId(),accountDetailBO.getAmount());
        return accountDetailBO;
    }

    @Override
    public PriceBO addPrice(PriceBO priceBO) {
        PricePO po = priceBO.transToPO();
        priceDAO.insert(po);
        BeanUtils.copyProperties(po,priceBO);
        return priceBO;
    }

    @Override
    public BizCallNumberBO addBizCallNumber(BizCallNumberBO bizCallNumberBO) {
        BizCallNumberPO po = bizCallNumberBO.transToPO();
        bizCallNumberDAO.save(po);
        BeanUtils.copyProperties(po,bizCallNumberBO);
        return bizCallNumberBO;
    }


}
