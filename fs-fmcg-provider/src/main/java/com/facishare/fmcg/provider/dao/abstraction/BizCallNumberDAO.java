package com.facishare.fmcg.provider.dao.abstraction;

import com.facishare.fmcg.provider.dao.po.BizCallNumberPO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/14 下午2:26
 */
public interface BizCallNumberDAO {

    String save(BizCallNumberPO po);

    void success(String id,Integer increment);

    void fail(String id,Integer increment);

    void addUser(String id, List<Integer> users);

    BizCallNumberPO get(String id);

    BizCallNumberPO query(Integer tenantId,String biz);

    void success(Integer tenantId,String biz,Integer increment);

    void fail(Integer tenantId,String biz,Integer increment);
}
