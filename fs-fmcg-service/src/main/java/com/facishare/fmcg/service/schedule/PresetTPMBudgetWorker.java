package com.facishare.fmcg.service.schedule;

import com.facishare.converter.EIEAConverter;
import com.facishare.fmcg.adapter.route.DBRouterAdapter;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.common.license.Active;
import com.facishare.fmcg.api.service.common.LicenseService;
import com.facishare.fmcg.api.service.tenant.TenantCommonService;
import com.facishare.fmcg.provider.dao.abstraction.LicenseDAO;
import com.facishare.fmcg.provider.dao.po.LicensePo;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * author: wuyx
 * description: 定时查灰度申请并预置预算管理
 * createTime: 2023/4/10 19:59
 */
@Slf4j
public class PresetTPMBudgetWorker extends TaskWorkerBase {

    private static final String LOCK_KEY = "FMCG_PRESET_TPM_BUDGET_TASK_LOCK";

    private static final String VARIABLES_FS_GRAY_PRODUCT_COMMA = "variables_fs_gray_product_comma";
    private static final String BUDGET_MANAGEMENT_GRAY_EI = "budget_management_gray_ei";

    @Resource
    private LicenseDAO licenseDao;

    @Resource
    private LicenseService licenseService;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private TenantCommonService tenantCommonService;

    public void run() {
        log.info("init PresetTPMBudgetWorker");

        String uuid = UUID.randomUUID().toString();
        if (tryGetLock(LOCK_KEY, uuid)) {
            try {
                String budgetManagementGayEi = ConfigFactory.getConfig(VARIABLES_FS_GRAY_PRODUCT_COMMA).get(BUDGET_MANAGEMENT_GRAY_EI);
                if (Strings.isNullOrEmpty(budgetManagementGayEi)) {
                    return;
                }
                List<String> budgetManagementGayEiList = Arrays.asList(budgetManagementGayEi.split(","));
                //营销活动1.0 ，已有预算2.0 ，预算1.0 ，手动升级到预算2.0 的企业，需要排除
                ArrayList<String> excludeCode = Lists.newArrayList("FMCG.TPM", "FMCG.TPM_BUDGET_ACCOUNT", "FMCG.TPM_BUDGET", "FMCG.TPM_BUDGET.2");

                List<LicensePo> licenses = licenseDao.queryAllTPMByTenantId(budgetManagementGayEiList.stream().map(Integer::parseInt).collect(Collectors.toList()), excludeCode);
                List<String> alreadyHasTenantIds = licenses.stream().map(licensePo -> String.valueOf(licensePo.getTenantId())).collect(Collectors.toList());
                log.info("PresetTPMBudgetWorker alreadyHasTenantIds = {}", alreadyHasTenantIds);

                List<String> needPreSetTenantIds = budgetManagementGayEiList.stream().filter(s -> !alreadyHasTenantIds.contains(s)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(needPreSetTenantIds)) {
                    log.error("PresetTPMBudgetWorker before active needPreSetTenantIds is empty");
                    return;
                }

                //预置active
                doActive(needPreSetTenantIds);
            } catch (Exception ex) {
                log.error("PresetTPMBudgetWorker run error.", ex);
                throw ex;
            } finally {
                releaseLock(LOCK_KEY, uuid);
            }
        }
    }


    private List<String> doActive(List<String> needPreSetTenantIds) {
        List<String> errorTenantIds = Lists.newArrayList();
        for (String tenantId : needPreSetTenantIds) {
            try {
                // 校验企业是否 存活
                if (Boolean.FALSE.equals(tenantCommonService.judgeIfTenantIsValid(Integer.parseInt(tenantId)))) {
                    continue;
                }

                Active.Arg arg = new Active.Arg();
                arg.setAppCode("FMCG.TPM_BUDGET_ACCOUNT");
                licenseService.active(fromApiArg(Integer.parseInt(tenantId), arg));
            } catch (Exception e) {
                log.error("PresetTPMBudgetWorker active err tenantId = {}", tenantId, e);
                errorTenantIds.add(tenantId);
            }
        }
        return errorTenantIds;
    }

    private <T extends ArgBase> ApiArg<T> fromApiArg(int tenantId, T data) {
        ApiArg<T> apiArg = new ApiArg<>();
        apiArg.setTenantAccount(eieaConverter.enterpriseIdToAccount(tenantId));
        apiArg.setTenantId(tenantId);
        apiArg.setUserId(-10000);
        apiArg.setData(data);
        return apiArg;
    }


}
