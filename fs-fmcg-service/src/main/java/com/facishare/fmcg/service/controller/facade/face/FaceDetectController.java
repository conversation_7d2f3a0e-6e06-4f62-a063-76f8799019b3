package com.facishare.fmcg.service.controller.facade.face;


import com.facishare.cep.plugin.annotation.FSOuterUserInfo;
import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.ai.face.FaceComparision;
import com.facishare.fmcg.api.dto.ai.face.IsExistFace;
import com.facishare.fmcg.api.dto.ai.face.AddFace;
import com.facishare.fmcg.api.error.FmcgException;
import com.facishare.fmcg.api.service.ai.face.FaceService;
import com.facishare.fmcg.service.common.ModelConverter;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @date 19-11-12  下午6:59
 */
@RestController
@RequestMapping(value = "face")
public class FaceDetectController {

    @Resource
    private FaceService faceService;

    @RequestMapping(value = "faceComparision")
    public FaceComparision.Result faceComparision(@FSOuterUserInfo OuterUserInfo outerUserInfo, @FSUserInfo UserInfo userInfo, @RequestBody FaceComparision.Arg arg) throws FmcgException {
        return faceService.faceComparison(ModelConverter.convert(userInfo, outerUserInfo, arg)).cepHandler();
    }

    @RequestMapping(value = "debug/faceComparision")
    public FaceComparision.Result faceComparision(@RequestBody ApiArg<FaceComparision.Arg> arg) throws FmcgException {
        return faceService.faceComparison(arg).cepHandler();
    }

    @RequestMapping(value = "addFace")
    public AddFace.Result addFace(@FSOuterUserInfo OuterUserInfo outerUserInfo, @FSUserInfo UserInfo userInfo, @RequestBody AddFace.Arg arg) throws FmcgException {
        return faceService.addFace(ModelConverter.convert(userInfo, outerUserInfo, arg)).cepHandler();
    }

    @RequestMapping(value = "debug/addFace")
    public AddFace.Result addFace(@RequestBody ApiArg<AddFace.Arg> arg) throws FmcgException {
        return faceService.addFace(arg).cepHandler();
    }

    @RequestMapping(value = "isExistFace")
    public IsExistFace.Result isExistFace(@FSOuterUserInfo OuterUserInfo outerUserInfo, @FSUserInfo UserInfo userInfo, @RequestBody IsExistFace.Arg arg) throws FmcgException {
        return faceService.isExistFace(ModelConverter.convert(userInfo, outerUserInfo, arg)).cepHandler();
    }
}
