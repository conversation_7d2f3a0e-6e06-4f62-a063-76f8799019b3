package com.facishare.fmcg.service.controller.facade.common;

import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.fmcg.api.dto.common.organization.AppGray;
import com.facishare.fmcg.api.service.common.AppGraService;
import com.facishare.fmcg.service.common.ModelConverter;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * Description
 * @date 2022-03-15 16:02
 **/
@RestController
@RequestMapping(value = "config", produces = "application/json")
public class AppConfigController {
    @Resource
    private AppGraService appGraService;

    @PostMapping(value = "/addGrayAndInitialization")
    public AppGray.Result addGrayAndInitialization(@FSUserInfo UserInfo context, @RequestBody AppGray.Arg arg) {
        return appGraService.addGrayAndInitialization(ModelConverter.convert(context, arg)).cepHandler();
    }

    @PostMapping(value = "/updateTPM2ActivityAuditConfig")
    public AppGray.Result updateTPM2ActivityAuditConfig(@FSUserInfo UserInfo context, @RequestBody AppGray.Arg arg) {
        return appGraService.updateTPM2ActivityAuditConfig(ModelConverter.convert(context, arg)).cepHandler();
    }

}
