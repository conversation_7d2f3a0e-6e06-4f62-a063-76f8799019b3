package com.facishare.fmcg.service.controller.facade.common;

import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.fmcg.api.dto.common.organization.RecordInit;
import com.facishare.fmcg.api.service.common.RecordInitService;
import com.facishare.fmcg.service.common.ModelConverter;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/record", produces = "application/json")
public class RecordController {
    @Resource
    private RecordInitService recordInitService;

    @PostMapping(value = "/init")
    public RecordInit.Result init(@FSUserInfo UserInfo context, @RequestBody RecordInit.Arg arg) {
        return recordInitService.initRecord(ModelConverter.convert(context, arg)).cepHandler();
    }
}
