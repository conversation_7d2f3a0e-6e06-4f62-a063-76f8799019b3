package com.facishare.fmcg.service.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.PaasDescribeProxy;
import com.fmcg.framework.http.contract.paas.data.PaasDataQueryWithFields;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeGet;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeUpdateField;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/1/12 2:07 PM
 */
@Slf4j
@SuppressWarnings("Duplicates")
public class SyncYuanQiProductCategoryWorker extends TaskWorkerBase {

    private static final String LOCK_KEY = "FMCG_YUAN_QI_SYNC_PRODUCT_CATEGORY_TASK_LOCK";

    private static final String NAME_FIELD_API_NAME = "name";
    private static final String CODE_FIELD_API_NAME = "code";

    protected static final Set<Integer> TENANT_ID_LIST = Sets.newHashSet(735463);
    protected static final List<ProductCategoryField> FIELDS = Lists.newArrayList(
            ProductCategoryField.builder().objectApiName("object_0Grr4__c").fieldApiName("field_4fdM5__c").build()
    );

    @Data
    @ToString
    @Builder
    static class ProductCategoryField {

        private String objectApiName;

        private String fieldApiName;
    }

    @Resource
    private PaasDataProxy paasDataProxy;

    @Resource
    private PaasDescribeProxy paasDescribeProxy;

    public void run() {
        String uuid = UUID.randomUUID().toString();
        if (tryGetLock(LOCK_KEY, uuid)) {
            for (Integer tenantId : TENANT_ID_LIST) {

                log.info("sync product category start : {}", tenantId);

                List<JSONObject> categoryList = queryCategory(tenantId);
                String newCodes = categoryList.stream().map(m -> m.getString(CODE_FIELD_API_NAME)).sorted().collect(Collectors.joining(","));

                log.info("sync product category new codes : {}", newCodes);

                for (ProductCategoryField field : FIELDS) {
                    log.info("sync product category start - api name : {}, field api name : {}", field.getObjectApiName(), field.getFieldApiName());

                    PaasDescribeGet.Result findDescribeResult = paasDescribeProxy.get(tenantId, -10000, field.getObjectApiName());
                    PaasDescribeGet.DescribeDTO describe = findDescribeResult.getData().getDescribe();
                    JSONObject fieldDescribe = describe.getFields().get(field.getFieldApiName());
                    JSONArray oldOptions = fieldDescribe.getJSONArray("options");
                    List<String> oldCodeList = Lists.newArrayList();
                    for (int i = 0; i < oldOptions.size(); i++) {
                        JSONObject oldOption = oldOptions.getJSONObject(i);
                        oldCodeList.add(oldOption.getString("value"));
                    }
                    String oldCodes = oldCodeList.stream().sorted().collect(Collectors.joining(","));

                    log.info("sync product category old codes : {}", oldCodes);

                    if (!newCodes.equals(oldCodes)) {
                        JSONArray newOptions = new JSONArray();
                        for (JSONObject category : categoryList) {
                            JSONObject newOption = new JSONObject();
                            newOption.put("value", category.getString(CODE_FIELD_API_NAME));
                            newOption.put("label", category.getString(NAME_FIELD_API_NAME));
                            newOptions.add(newOption);
                        }
                        fieldDescribe.put("options", newOptions);
                        PaasDescribeUpdateField.Result updateFieldResult = paasDescribeProxy.updateField(tenantId, -10000, field.getObjectApiName(), field.getFieldApiName(), fieldDescribe);
                        log.info("update field end : {}", JSON.toJSONString(updateFieldResult));
                    }
                }
            }
        }
    }

    private List<JSONObject> queryCategory(int tenantId) {
        PaasDataQueryWithFields.QueryDTO query = new PaasDataQueryWithFields.QueryDTO.Builder()
                .limit(200)
                .build();
        PaasDataQueryWithFields.Arg arg = new PaasDataQueryWithFields.Arg();
        arg.setFieldList(Lists.newArrayList(NAME_FIELD_API_NAME, CODE_FIELD_API_NAME));
        arg.setQueryString(JSON.toJSONString(query));
        return paasDataProxy.queryWithFields(tenantId, -10000, "ProductCategoryObj", arg).getResult().getQueryResult().getDataList();
    }
}
