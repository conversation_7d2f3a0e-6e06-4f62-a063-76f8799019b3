{"describe_api_name": "TPMActivityAgreementObj", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "EQ", "field_name": "is_agreement_required", "field_values": ["true"]}, {"value_type": 0, "operator": "EQ", "field_name": "activity_status", "field_values": ["in_progress"]}, {"value_type": 0, "operator": "EQ", "field_name": "life_status", "field_values": ["normal"]}]}], "define_type": "package", "input_mode": "", "is_single": false, "index_name": "s_3", "is_index": true, "is_active": true, "is_encrypted": false, "label": "活动方案", "target_api_name": "TPMActivityObj", "target_related_list_name": "target_related_list_TPMActivityAgreementObj_TPMActivityObj__c", "target_related_list_label": "活动协议", "action_on_target_delete": "set_null", "api_name": "activity_id", "is_index_field": true, "help_text": "", "status": "new"}