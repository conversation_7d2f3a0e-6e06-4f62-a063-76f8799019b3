CREATE TABLE IF NOT EXISTS fmcg_tpm_proof_time_period_detail (
    id  VARCHAR(64) NOT NULL,
    tenant_id  VARCHAR(32) NOT NULL,
    name  VARCHAR(128),
    proof_number NUMERIC,
    end_date NUMERIC,
    proof_disagree_number NUMERIC,
    proof_period VARCHAR(100),
    agreement_id VARCHAR(64),
    proof_status VARCHAR(64),
    stage NUMERIC,
    begin_date NUMERIC,
    activity_id VARCHAR(64),
    proof_agree_number NUMERIC,
    achievement_status VARCHAR(64),
    source_type VARCHAR(64),
    owner  VARCHAR(64),
    display_name  VARCHAR(128),
    lock_status  VARCHAR(64),
    life_status  VARCHAR(64),
    record_type  VARCHAR(64),
    created_by  VARC<PERSON>R(64),
    create_time BIGINT,
    last_modified_by  VARCHAR(64),
    last_modified_time BIGINT,
    extend_obj_data_id  VARCHAR(128),
    package  VARCHAR(64),
    object_describe_id  VARCHAR(128),
    object_describe_api_name  <PERSON><PERSON>HA<PERSON>(128),
    version INT,
    lock_user  VARCHAR(128),
    lock_rule  VARCHAR(128),
    life_status_before_invalid  VARCHAR(64),
    is_deleted INT,
    out_tenant_id  VARCHAR(50),
    out_owner  VARCHAR(50),
    data_own_department  VARCHAR(32),
    data_own_organization  VARCHAR(32),
    out_data_own_department  VARCHAR(64),
    out_data_own_organization  VARCHAR(64),
    data_auth_code  VARCHAR(64),
    change_type INT,
    out_data_auth_code  VARCHAR(64),
    order_by INT,
    data_auth_id INT,
    out_data_auth_id INT,
    sys_modified_time BIGINT,
    origin_source varchar(128),
    dimension_d1 VARCHAR(2000)[64],
    dimension_d2 VARCHAR(2000)[64],
    dimension_d3 VARCHAR(2000)[64],
    mc_currency VARCHAR(128) ,
    mc_exchange_rate NUMERIC,
    mc_functional_currency VARCHAR(128),
    mc_exchange_rate_version VARCHAR(64) ,
    CONSTRAINT fmcg_tpm_proof_time_period_detail_pk PRIMARY KEY (id, tenant_id)
    );

CREATE INDEX  IF NOT EXISTS fmcg_tpm_proof_time_period_detail_id_name_delete_describe_index ON fmcg_tpm_proof_time_period_detail USING btree(tenant_id,name, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_proof_time_period_detail_ti_dn_id_odan_index ON fmcg_tpm_proof_time_period_detail USING btree(tenant_id,display_name,is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_proof_time_period_detail_eodi_ti_index ON fmcg_tpm_proof_time_period_detail USING btree(extend_obj_data_id,tenant_id);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_proof_time_period_detail_lmt_ti_id_odan_index ON fmcg_tpm_proof_time_period_detail USING btree(last_modified_time desc,tenant_id,is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_proof_time_period_detail_ct_ti_id_odan_index ON fmcg_tpm_proof_time_period_detail USING btree(create_time desc,tenant_id, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_proof_time_period_detail_ti_o_id_odan_index ON fmcg_tpm_proof_time_period_detail USING btree(tenant_id,owner, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_proof_time_period_detail_ti_dod_id_odan_index ON fmcg_tpm_proof_time_period_detail USING btree(tenant_id,data_own_department, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_proof_time_period_detail_dac_index ON fmcg_tpm_proof_time_period_detail USING btree(data_auth_code);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_proof_time_period_detail_odac_index ON fmcg_tpm_proof_time_period_detail USING btree(out_data_auth_code);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_proof_time_period_detail_out_id_ti_index ON fmcg_tpm_proof_time_period_detail USING btree(out_tenant_id,is_deleted,tenant_id);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_proof_time_period_detail_smt_ti_id_odan_index ON fmcg_tpm_proof_time_period_detail USING btree(sys_modified_time desc,tenant_id, is_deleted, object_describe_api_name);
DROP TRIGGER IF EXISTS x_audit_changes ON fmcg_tpm_proof_time_period_detail;
DROP TRIGGER IF EXISTS x_system_changes ON fmcg_tpm_proof_time_period_detail;
CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON fmcg_tpm_proof_time_period_detail FOR EACH ROW EXECUTE PROCEDURE f_system_change();
CREATE TRIGGER x_audit_changes AFTER INSERT OR UPDATE OR DELETE ON fmcg_tpm_proof_time_period_detail FOR EACH ROW EXECUTE PROCEDURE f_change_detail('id','tenant_id','object_describe_api_name');

CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_tpm_proof_time_period_detail_ai_ti_id_idx ON fmcg_tpm_proof_time_period_detail USING BTREE (activity_id,tenant_id,is_deleted);
CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_tpm_proof_time_period_detail_ai_ti_id_idx ON fmcg_tpm_proof_time_period_detail USING BTREE (agreement_id,tenant_id,is_deleted);

CREATE TABLE IF NOT EXISTS fmcg_tpm_activity_proof_material_detail (
    id  VARCHAR(64) NOT NULL,
    tenant_id  VARCHAR(32) NOT NULL,
    name  VARCHAR(128),
    material_category VARCHAR(64),
    ai_number NUMERIC,
    number_standard NUMERIC,
    display_form_id VARCHAR(64),
    material_id VARCHAR(64),
    activity_proof_id VARCHAR(64),
    activity_proof_display_img_id VARCHAR(64),
    owner  VARCHAR(64),
    display_name  VARCHAR(128),
    lock_status  VARCHAR(64),
    life_status  VARCHAR(64),
    record_type  VARCHAR(64),
    created_by  VARCHAR(64),
    create_time BIGINT,
    last_modified_by  VARCHAR(64),
    last_modified_time BIGINT,
    extend_obj_data_id  VARCHAR(128),
    package  VARCHAR(64),
    object_describe_id  VARCHAR(128),
    object_describe_api_name  VARCHAR(128),
    version INT,
    lock_user  VARCHAR(128),
    lock_rule  VARCHAR(128),
    life_status_before_invalid  VARCHAR(64),
    is_deleted INT,
    out_tenant_id  VARCHAR(50),
    out_owner  VARCHAR(50),
    data_own_department  VARCHAR(32),
    data_own_organization  VARCHAR(32),
    out_data_own_department  VARCHAR(64),
    out_data_own_organization  VARCHAR(64),
    data_auth_code  VARCHAR(64),
    change_type INT,
    out_data_auth_code  VARCHAR(64),
    order_by INT,
    data_auth_id INT,
    out_data_auth_id INT,
    sys_modified_time BIGINT,
    origin_source varchar(128),
    dimension_d1 VARCHAR(2000)[64],
    dimension_d2 VARCHAR(2000)[64],
    dimension_d3 VARCHAR(2000)[64],
    mc_currency VARCHAR(128) ,
    mc_exchange_rate NUMERIC,
    mc_functional_currency VARCHAR(128),
    mc_exchange_rate_version VARCHAR(64) ,
    CONSTRAINT fmcg_tpm_activity_proof_material_detail_pk PRIMARY KEY (id, tenant_id)
    );

CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_material_detail_id_name_delete_describe_index ON fmcg_tpm_activity_proof_material_detail USING btree(tenant_id,name, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_material_detail_ti_dn_id_odan_index ON fmcg_tpm_activity_proof_material_detail USING btree(tenant_id,display_name,is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_material_detail_eodi_ti_index ON fmcg_tpm_activity_proof_material_detail USING btree(extend_obj_data_id,tenant_id);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_material_detail_lmt_ti_id_odan_index ON fmcg_tpm_activity_proof_material_detail USING btree(last_modified_time desc,tenant_id,is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_material_detail_ct_ti_id_odan_index ON fmcg_tpm_activity_proof_material_detail USING btree(create_time desc,tenant_id, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_material_detail_ti_o_id_odan_index ON fmcg_tpm_activity_proof_material_detail USING btree(tenant_id,owner, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_material_detail_ti_dod_id_odan_index ON fmcg_tpm_activity_proof_material_detail USING btree(tenant_id,data_own_department, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_material_detail_dac_index ON fmcg_tpm_activity_proof_material_detail USING btree(data_auth_code);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_material_detail_odac_index ON fmcg_tpm_activity_proof_material_detail USING btree(out_data_auth_code);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_material_detail_out_id_ti_index ON fmcg_tpm_activity_proof_material_detail USING btree(out_tenant_id,is_deleted,tenant_id);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_material_detail_smt_ti_id_odan_index ON fmcg_tpm_activity_proof_material_detail USING btree(sys_modified_time desc,tenant_id, is_deleted, object_describe_api_name);
DROP TRIGGER IF EXISTS x_audit_changes ON fmcg_tpm_activity_proof_material_detail;
DROP TRIGGER IF EXISTS x_system_changes ON fmcg_tpm_activity_proof_material_detail;
CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON fmcg_tpm_activity_proof_material_detail FOR EACH ROW EXECUTE PROCEDURE f_system_change();
CREATE TRIGGER x_audit_changes AFTER INSERT OR UPDATE OR DELETE ON fmcg_tpm_activity_proof_material_detail FOR EACH ROW EXECUTE PROCEDURE f_change_detail('id','tenant_id','object_describe_api_name');

CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_tpm_activity_proof_material_detail_apdfi_ti_id_idx ON fmcg_tpm_activity_proof_material_detail USING BTREE (activity_proof_display_img_id,tenant_id,is_deleted);
CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_tpm_activity_proof_material_detail_dfi_ti_id_idx ON fmcg_tpm_activity_proof_material_detail USING BTREE (display_form_id,tenant_id,is_deleted);
CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_tpm_activity_proof_material_detail_mi_ti_id_idx ON fmcg_tpm_activity_proof_material_detail USING BTREE (material_id,tenant_id,is_deleted);
CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_tpm_activity_proof_material_detail_api_ti_id_idx ON fmcg_tpm_activity_proof_material_detail USING BTREE (activity_proof_id,tenant_id,is_deleted);

CREATE TABLE IF NOT EXISTS fmcg_tpm_activity_proof_display_img (
    id  VARCHAR(64) NOT NULL,
    tenant_id  VARCHAR(32) NOT NULL,
    name  VARCHAR(128),
    image VARCHAR(1000),
    system_judgment_status VARCHAR(64),
    actual_display_position VARCHAR(200),
    audit_status VARCHAR(64),
    standard_display_position VARCHAR(200),
    display_form_id VARCHAR(64),
    activity_item_id VARCHAR(64),
    activity_proof_id VARCHAR(64),
    standard_description VARCHAR(2000),
    ai_display_form_id VARCHAR(2000)[64],
    ai_layer_number NUMERIC,
    ai_group_number NUMERIC,
    ai_visible_number NUMERIC,
    owner  VARCHAR(64),
    display_name  VARCHAR(128),
    lock_status  VARCHAR(64),
    life_status  VARCHAR(64),
    record_type  VARCHAR(64),
    created_by  VARCHAR(64),
    create_time BIGINT,
    last_modified_by  VARCHAR(64),
    last_modified_time BIGINT,
    extend_obj_data_id  VARCHAR(128),
    package  VARCHAR(64),
    object_describe_id  VARCHAR(128),
    object_describe_api_name  VARCHAR(128),
    version INT,
    lock_user  VARCHAR(128),
    lock_rule  VARCHAR(128),
    life_status_before_invalid  VARCHAR(64),
    is_deleted INT,
    out_tenant_id  VARCHAR(50),
    out_owner  VARCHAR(50),
    data_own_department  VARCHAR(32),
    data_own_organization  VARCHAR(32),
    out_data_own_department  VARCHAR(64),
    out_data_own_organization  VARCHAR(64),
    data_auth_code  VARCHAR(64),
    change_type INT,
    out_data_auth_code  VARCHAR(64),
    order_by INT,
    data_auth_id INT,
    out_data_auth_id INT,
    sys_modified_time BIGINT,
    origin_source varchar(128),
    dimension_d1 VARCHAR(2000)[64],
    dimension_d2 VARCHAR(2000)[64],
    dimension_d3 VARCHAR(2000)[64],
    mc_currency VARCHAR(128) ,
    mc_exchange_rate NUMERIC,
    mc_functional_currency VARCHAR(128),
    mc_exchange_rate_version VARCHAR(64) ,
    CONSTRAINT fmcg_tpm_activity_proof_display_img_pk PRIMARY KEY (id, tenant_id)
    );

CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_display_img_id_name_delete_describe_index ON fmcg_tpm_activity_proof_display_img USING btree(tenant_id,name, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_display_img_ti_dn_id_odan_index ON fmcg_tpm_activity_proof_display_img USING btree(tenant_id,display_name,is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_display_img_eodi_ti_index ON fmcg_tpm_activity_proof_display_img USING btree(extend_obj_data_id,tenant_id);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_display_img_lmt_ti_id_odan_index ON fmcg_tpm_activity_proof_display_img USING btree(last_modified_time desc,tenant_id,is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_display_img_ct_ti_id_odan_index ON fmcg_tpm_activity_proof_display_img USING btree(create_time desc,tenant_id, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_display_img_ti_o_id_odan_index ON fmcg_tpm_activity_proof_display_img USING btree(tenant_id,owner, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_display_img_ti_dod_id_odan_index ON fmcg_tpm_activity_proof_display_img USING btree(tenant_id,data_own_department, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_display_img_dac_index ON fmcg_tpm_activity_proof_display_img USING btree(data_auth_code);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_display_img_odac_index ON fmcg_tpm_activity_proof_display_img USING btree(out_data_auth_code);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_display_img_out_id_ti_index ON fmcg_tpm_activity_proof_display_img USING btree(out_tenant_id,is_deleted,tenant_id);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_display_img_smt_ti_id_odan_index ON fmcg_tpm_activity_proof_display_img USING btree(sys_modified_time desc,tenant_id, is_deleted, object_describe_api_name);
DROP TRIGGER IF EXISTS x_audit_changes ON fmcg_tpm_activity_proof_display_img;
DROP TRIGGER IF EXISTS x_system_changes ON fmcg_tpm_activity_proof_display_img;
CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON fmcg_tpm_activity_proof_display_img FOR EACH ROW EXECUTE PROCEDURE f_system_change();
CREATE TRIGGER x_audit_changes AFTER INSERT OR UPDATE OR DELETE ON fmcg_tpm_activity_proof_display_img FOR EACH ROW EXECUTE PROCEDURE f_change_detail('id','tenant_id','object_describe_api_name');

CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_tpm_activity_proof_display_img_api_ti_id_idx ON fmcg_tpm_activity_proof_display_img USING BTREE (activity_proof_id,tenant_id,is_deleted);
CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_tpm_activity_proof_display_img_dfi_ti_id_idx ON fmcg_tpm_activity_proof_display_img USING BTREE (display_form_id,tenant_id,is_deleted);
CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_tpm_activity_proof_display_img_aii_ti_id_idx ON fmcg_tpm_activity_proof_display_img USING BTREE (activity_item_id,tenant_id,is_deleted);
CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_tpm_activity_proof_display_img_aaii_ti_id_idx ON fmcg_tpm_activity_proof_display_img USING BTREE (ai_display_form_id,tenant_id,is_deleted);

CREATE TABLE IF NOT EXISTS fmcg_tpm_activity_proof_product_detail (
    id  VARCHAR(64) NOT NULL,
    tenant_id  VARCHAR(32) NOT NULL,
    name  VARCHAR(128),
    ai_number NUMERIC,
    number_standard NUMERIC,
    display_form_id VARCHAR(64),
    product_id VARCHAR(64),
    activity_item_id VARCHAR(64),
    activity_proof_id VARCHAR(64),
    activity_proof_display_img_id VARCHAR(64),
    product_category_id VARCHAR(64),
    owner  VARCHAR(64),
    display_name  VARCHAR(128),
    lock_status  VARCHAR(64),
    life_status  VARCHAR(64),
    record_type  VARCHAR(64),
    created_by  VARCHAR(64),
    create_time BIGINT,
    last_modified_by  VARCHAR(64),
    last_modified_time BIGINT,
    extend_obj_data_id  VARCHAR(128),
    package  VARCHAR(64),
    object_describe_id  VARCHAR(128),
    object_describe_api_name  VARCHAR(128),
    version INT,
    lock_user  VARCHAR(128),
    lock_rule  VARCHAR(128),
    life_status_before_invalid  VARCHAR(64),
    is_deleted INT,
    out_tenant_id  VARCHAR(50),
    out_owner  VARCHAR(50),
    data_own_department  VARCHAR(32),
    data_own_organization  VARCHAR(32),
    out_data_own_department  VARCHAR(64),
    out_data_own_organization  VARCHAR(64),
    data_auth_code  VARCHAR(64),
    change_type INT,
    out_data_auth_code  VARCHAR(64),
    order_by INT,
    data_auth_id INT,
    out_data_auth_id INT,
    sys_modified_time BIGINT,
    origin_source varchar(128),
    dimension_d1 VARCHAR(2000)[64],
    dimension_d2 VARCHAR(2000)[64],
    dimension_d3 VARCHAR(2000)[64],
    mc_currency VARCHAR(128) ,
    mc_exchange_rate NUMERIC,
    mc_functional_currency VARCHAR(128),
    mc_exchange_rate_version VARCHAR(64) ,
    CONSTRAINT fmcg_tpm_activity_proof_product_detail_pk PRIMARY KEY (id, tenant_id)
    );

CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_product_detail_id_name_delete_describe_index ON fmcg_tpm_activity_proof_product_detail USING btree(tenant_id,name, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_product_detail_ti_dn_id_odan_index ON fmcg_tpm_activity_proof_product_detail USING btree(tenant_id,display_name,is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_product_detail_eodi_ti_index ON fmcg_tpm_activity_proof_product_detail USING btree(extend_obj_data_id,tenant_id);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_product_detail_lmt_ti_id_odan_index ON fmcg_tpm_activity_proof_product_detail USING btree(last_modified_time desc,tenant_id,is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_product_detail_ct_ti_id_odan_index ON fmcg_tpm_activity_proof_product_detail USING btree(create_time desc,tenant_id, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_product_detail_ti_o_id_odan_index ON fmcg_tpm_activity_proof_product_detail USING btree(tenant_id,owner, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_product_detail_ti_dod_id_odan_index ON fmcg_tpm_activity_proof_product_detail USING btree(tenant_id,data_own_department, is_deleted, object_describe_api_name);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_product_detail_dac_index ON fmcg_tpm_activity_proof_product_detail USING btree(data_auth_code);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_product_detail_odac_index ON fmcg_tpm_activity_proof_product_detail USING btree(out_data_auth_code);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_product_detail_out_id_ti_index ON fmcg_tpm_activity_proof_product_detail USING btree(out_tenant_id,is_deleted,tenant_id);
CREATE INDEX  IF NOT EXISTS fmcg_tpm_activity_proof_product_detail_smt_ti_id_odan_index ON fmcg_tpm_activity_proof_product_detail USING btree(sys_modified_time desc,tenant_id, is_deleted, object_describe_api_name);
DROP TRIGGER IF EXISTS x_audit_changes ON fmcg_tpm_activity_proof_product_detail;
DROP TRIGGER IF EXISTS x_system_changes ON fmcg_tpm_activity_proof_product_detail;
CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON fmcg_tpm_activity_proof_product_detail FOR EACH ROW EXECUTE PROCEDURE f_system_change();
CREATE TRIGGER x_audit_changes AFTER INSERT OR UPDATE OR DELETE ON fmcg_tpm_activity_proof_product_detail FOR EACH ROW EXECUTE PROCEDURE f_change_detail('id','tenant_id','object_describe_api_name');

CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_tpm_activity_proof_product_detail_apdii_ti_id_idx ON fmcg_tpm_activity_proof_product_detail USING BTREE (activity_proof_display_img_id,tenant_id,is_deleted);
CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_tpm_activity_proof_product_detail_pci_ti_id_idx ON fmcg_tpm_activity_proof_product_detail USING BTREE (product_category_id,tenant_id,is_deleted);
CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_tpm_activity_proof_product_detail_aii_ti_id_idx ON fmcg_tpm_activity_proof_product_detail USING BTREE (activity_item_id,tenant_id,is_deleted);
CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_tpm_activity_proof_product_detail_api_ti_id_idx ON fmcg_tpm_activity_proof_product_detail USING BTREE (activity_proof_id,tenant_id,is_deleted);
CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_tpm_activity_proof_product_detail_pi_ti_id_idx ON fmcg_tpm_activity_proof_product_detail USING BTREE (product_id,tenant_id,is_deleted);
CREATE INDEX CONCURRENTLY IF NOT EXISTS fmcg_tpm_activity_proof_product_detail_dfi_ti_id_idx ON fmcg_tpm_activity_proof_product_detail USING BTREE (display_form_id,tenant_id,is_deleted);

ALTER TABLE IF EXISTS fmcg_tpm_activity ADD COLUMN IF NOT EXISTS proof_period VARCHAR(40000);

ALTER TABLE IF EXISTS fmcg_tpm_activity_detail ADD COLUMN IF NOT EXISTS product_item_standard_id VARCHAR(64);
ALTER TABLE IF EXISTS fmcg_tpm_activity_detail ADD COLUMN IF NOT EXISTS product_standard_description VARCHAR(1000);
ALTER TABLE IF EXISTS fmcg_tpm_activity_detail ADD COLUMN IF NOT EXISTS material_standard_requirem_id VARCHAR(64);
ALTER TABLE IF EXISTS fmcg_tpm_activity_detail ADD COLUMN IF NOT EXISTS material_standard_description VARCHAR(1000);
ALTER TABLE IF EXISTS fmcg_tpm_activity_detail ADD COLUMN IF NOT EXISTS display_form_id VARCHAR(64);

CREATE INDEX IF NOT EXISTS fmcg_tpm_activity_detail_product_item_standard_id_idx ON fmcg_tpm_activity_detail USING BTREE (product_item_standard_id,tenant_id,is_deleted);
CREATE INDEX IF NOT EXISTS fmcg_tpm_activity_detail_material_standard_requirem_id_idx ON fmcg_tpm_activity_detail USING BTREE (material_standard_requirem_id,tenant_id,is_deleted);
CREATE INDEX IF NOT EXISTS fmcg_tpm_activity_detail_display_form_id_idx ON fmcg_tpm_activity_detail USING BTREE (display_form_id,tenant_id,is_deleted);

ALTER TABLE IF EXISTS fmcg_tpm_activity_agreement_detail ADD COLUMN IF NOT EXISTS product_item_standard_id VARCHAR(64);
ALTER TABLE IF EXISTS fmcg_tpm_activity_agreement_detail ADD COLUMN IF NOT EXISTS product_standard_description VARCHAR(1000);
ALTER TABLE IF EXISTS fmcg_tpm_activity_agreement_detail ADD COLUMN IF NOT EXISTS material_standard_requirem_id VARCHAR(64);
ALTER TABLE IF EXISTS fmcg_tpm_activity_agreement_detail ADD COLUMN IF NOT EXISTS material_standard_description VARCHAR(1000);
ALTER TABLE IF EXISTS fmcg_tpm_activity_agreement_detail ADD COLUMN IF NOT EXISTS display_form_id VARCHAR(64);

CREATE INDEX IF NOT EXISTS fmcg_tpm_activity_agreement_detail_product_item_standard_id_idx ON fmcg_tpm_activity_agreement_detail USING BTREE (product_item_standard_id,tenant_id,is_deleted);
CREATE INDEX IF NOT EXISTS fmcg_tpm_activity_agreement_detail_material_standard_requirem_id_idx ON fmcg_tpm_activity_agreement_detail USING BTREE (material_standard_requirem_id,tenant_id,is_deleted);
CREATE INDEX IF NOT EXISTS fmcg_tpm_activity_agreement_detail_display_form_id_idx ON fmcg_tpm_activity_agreement_detail USING BTREE (display_form_id,tenant_id,is_deleted);

ALTER TABLE IF EXISTS fmcg_tpm_activity_proof ADD COLUMN IF NOT EXISTS ai_identify_status VARCHAR(64);
ALTER TABLE IF EXISTS fmcg_tpm_activity_proof ADD COLUMN IF NOT EXISTS system_judgment_status VARCHAR(64);
ALTER TABLE IF EXISTS fmcg_tpm_activity_proof ADD COLUMN IF NOT EXISTS open_ai BOOLEAN;
ALTER TABLE IF EXISTS fmcg_tpm_activity_proof ADD COLUMN IF NOT EXISTS proof_time_period_detail_id VARCHAR(64);

CREATE INDEX IF NOT EXISTS fmcg_tpm_activity_proof_proof_time_period_detail_id_idx ON fmcg_tpm_activity_proof USING BTREE (proof_time_period_detail_id,tenant_id,is_deleted);

ALTER TABLE IF EXISTS fmcg_tpm_activity_proof_detail ADD COLUMN IF NOT EXISTS ai_number NUMERIC;
ALTER TABLE IF EXISTS fmcg_tpm_activity_proof_detail ADD COLUMN IF NOT EXISTS ai_face_number NUMERIC;
ALTER TABLE IF EXISTS fmcg_tpm_activity_proof_detail ADD COLUMN IF NOT EXISTS ai_sku_number NUMERIC;
ALTER TABLE IF EXISTS fmcg_tpm_activity_proof_detail ADD COLUMN IF NOT EXISTS system_judgment_status VARCHAR(64);
ALTER TABLE IF EXISTS fmcg_tpm_activity_proof_detail ADD COLUMN IF NOT EXISTS achievement_status VARCHAR(64);
ALTER TABLE IF EXISTS fmcg_tpm_activity_proof_detail ADD COLUMN IF NOT EXISTS product_display_status VARCHAR(64);
ALTER TABLE IF EXISTS fmcg_tpm_activity_proof_detail ADD COLUMN IF NOT EXISTS material_display_status VARCHAR(64);
ALTER TABLE IF EXISTS fmcg_tpm_activity_proof_detail ADD COLUMN IF NOT EXISTS display_form_id VARCHAR(64);

CREATE INDEX IF NOT EXISTS fmcg_tpm_activity_proof_detail_display_form_id_idx ON fmcg_tpm_activity_proof_detail USING BTREE (display_form_id,tenant_id,is_deleted);

ALTER TABLE IF EXISTS fmcg_tpm_activity_item ADD COLUMN IF NOT EXISTS display_projects_id VARCHAR(64);
CREATE INDEX IF NOT EXISTS fmcg_tpm_activity_item_dpi_ti_id_idx ON fmcg_tpm_activity_item USING BTREE (display_projects_id,tenant_id,is_deleted);
