{"store_table_name": "fmcg_tpm_budget_provision_record", "description": "", "enabled_change_order": false, "index_version": 1, "is_deleted": false, "define_type": "package", "release_version": "6.4", "package": "CRM", "is_active": true, "display_name": "预算预提记录", "is_open_display_name": false, "icon_index": 0, "api_name": "TPMBudgetProvisionRecordObj", "icon_path": "", "short_name": "bpr", "fields": {"tenant_id": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "租户ID", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "lock_rule": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "label_r": "锁定规则", "status": "new"}, "origin_source": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": false, "is_active": true, "is_unique": false, "label": "数据来源", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "origin_source", "options": [{"label": "数据同步", "value": "0"}], "define_type": "system", "status": "released"}, "lock_user": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "label_r": "加锁人", "status": "new"}, "extend_obj_data_id": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": true, "is_required": false, "api_name": "extend_obj_data_id", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "status": "released", "max_length": 64}, "is_deleted": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": false, "is_unique": false, "description": "is_deleted", "default_value": "false", "label": "删除状态", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "label_r": "作废前生命状态", "max_length": 256, "status": "new"}, "object_describe_api_name": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "max_length": 200, "status": "released"}, "owner_department": {"describe_api_name": "TPMBudgetProvisionRecordObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "label_r": "负责人主属部门", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_show_mask": false, "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": true, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "status": "released"}, "owner": {"describe_api_name": "TPMBudgetProvisionRecordObj", "default_is_expression": false, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "default_value": "", "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "wheres": [], "api_name": "owner", "define_type": "package", "is_single": true, "label_r": "负责人", "help_text": "", "status": "new"}, "amount": {"describe_api_name": "TPMBudgetProvisionRecordObj", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "enable_clone": true, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "length": 12, "default_value": "", "label": "金额", "currency_unit": "￥", "currency_type": "oc", "api_name": "amount", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "label_r": "锁定状态", "status": "new"}, "package": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "create_time": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "life_status": {"describe_api_name": "TPMBudgetProvisionRecordObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "disable_after_filter": true, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "label_r": "生命状态", "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "normal", "label": "生命状态", "is_need_convert": false, "api_name": "life_status", "help_text": "", "status": "new"}, "budget_provision_id": {"describe_api_name": "TPMBudgetProvisionRecordObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "enable_clone": true, "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "预提表", "target_api_name": "TPMBudgetProvisionObj", "target_related_list_name": "target_related_list_TPMBudgetProvisionRecordObj_TPMBudgetProvisionObj__c", "target_related_list_label": "预算预提记录", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "budget_provision_id", "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "status": "released"}, "out_tenant_id": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "version": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "版本", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "status": "released"}, "relevant_team": {"describe_api_name": "TPMBudgetProvisionRecordObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "label_r": "相关团队", "help_text": "相关团队", "status": "new"}, "record_type": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "label_r": "业务类型", "help_text": "", "status": "released"}, "data_own_department": {"describe_api_name": "TPMBudgetProvisionRecordObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "department", "is_required": false, "wheres": [], "optional_type": "department", "define_type": "package", "is_single": true, "label_r": "归属部门", "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "归属部门", "is_need_convert": false, "api_name": "data_own_department", "help_text": "", "status": "new"}, "consume_node_id": {"describe_api_name": "TPMBudgetProvisionRecordObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "enable_clone": true, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "规则模块id", "api_name": "consume_node_id", "is_show_mask": false, "help_text": "", "status": "new"}, "related_object_data_id": {"describe_api_name": "TPMBudgetProvisionRecordObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "text", "default_to_zero": false, "used_in": "component", "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 256, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "对象数据id", "api_name": "related_object_data_id", "status": "new", "help_text": ""}, "related_object_api_name": {"describe_api_name": "TPMBudgetProvisionRecordObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "text", "default_to_zero": false, "used_in": "component", "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 256, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "对象apiName", "api_name": "related_object_api_name", "status": "new", "help_text": ""}, "related_object": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": false, "id_field": "related_object_data_id", "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "label": "业务对象", "group_type": "what", "type": "group", "is_need_convert": false, "is_required": false, "api_name": "related_object", "define_type": "package", "fields": {"id_field": "related_object_data_id", "api_name_field": "related_object_api_name"}, "is_single": false, "api_name_field": "related_object_api_name", "status": "new", "help_text": ""}, "name": {"describe_api_name": "TPMBudgetProvisionRecordObj", "default_is_expression": false, "prefix": "BPR-{yyyy}-{mm}-{dd}-", "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": true, "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "postfix": "", "input_mode": "", "is_single": false, "label_r": "主属性", "max_length": 100, "is_index": true, "is_active": true, "auto_number_type": "normal", "is_encrypted": false, "serial_number": 6, "default_value": "BPR-{yyyy}-{mm}-{dd}-000001", "label": "主属性", "condition": "MONTH", "api_name": "name", "func_api_name": "", "is_show_mask": false, "help_text": "", "status": "new"}, "order_by": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": false, "length": 8, "is_unique": false, "description": "order_by", "label": "序号", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "round_mode": 4, "status": "released"}, "_id": {"describe_api_name": "TPMBudgetProvisionRecordObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "_id", "label": "ID", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "max_length": 200, "status": "released"}}}