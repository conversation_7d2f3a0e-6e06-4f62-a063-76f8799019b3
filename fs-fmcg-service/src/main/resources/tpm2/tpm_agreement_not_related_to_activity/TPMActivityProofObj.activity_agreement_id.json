{"describe_api_name": "TPMActivityProofObj", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [{"connector": "OR", "filters": [{"value_type": 2, "operator": "EQ", "field_name": "store_id", "field_values": ["$store_id$"]}]}], "define_type": "package", "input_mode": "", "is_single": false, "cascade_parent_api_name": "[\"activity_id\",\"store_id\"]", "index_name": "s_3", "is_index": true, "is_active": true, "is_encrypted": false, "label": "活动协议", "target_api_name": "TPMActivityAgreementObj", "target_related_list_name": "target_related_list_TPMActivityProofObj_TPMActivityAgreementObj__c", "target_related_list_label": "活动举证", "lookup_roles": ["0_1_r", "0_4_r"], "action_on_target_delete": "set_null", "api_name": "activity_agreement_id", "is_index_field": true, "help_text": "", "status": "new"}