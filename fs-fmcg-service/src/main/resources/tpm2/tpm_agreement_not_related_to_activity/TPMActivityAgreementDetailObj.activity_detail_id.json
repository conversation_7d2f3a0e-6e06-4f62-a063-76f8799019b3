{"describe_api_name": "TPMActivityAgreementDetailObj", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [{"connector": "OR", "filters": [{"value_type": 2, "operator": "EQ", "field_name": "activity_id", "field_values": ["$activity_agreement_id__r.activity_id$"]}]}], "define_type": "package", "input_mode": "", "is_single": false, "index_name": "s_2", "is_index": true, "is_active": true, "is_encrypted": false, "label": "活动方案项目", "target_api_name": "TPMActivityDetailObj", "target_related_list_name": "target_related_list_TPMActivityAgreementDetailObj_TPMActivityDetailObj__c", "target_related_list_label": "活动协议项目", "action_on_target_delete": "set_null", "api_name": "activity_detail_id", "is_index_field": true, "help_text": "", "status": "new"}