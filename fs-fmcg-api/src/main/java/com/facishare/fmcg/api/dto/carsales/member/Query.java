package com.facishare.fmcg.api.dto.carsales.member;

import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.ResultBase;
import com.facishare.fmcg.api.dto.carsales.model.MemberVo;
import com.facishare.fmcg.api.error.FmcgException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

public interface Query {
    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Arg extends ArgBase {

        private int index;

        private int size;

        @Override
        public void insure() {

        }

        @Override
        public void validate() throws FmcgException {

        }
    }


    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Result extends ResultBase {

        private List<MemberVo> members;

        private long total;

        private int index;

        private int size;
    }
}
