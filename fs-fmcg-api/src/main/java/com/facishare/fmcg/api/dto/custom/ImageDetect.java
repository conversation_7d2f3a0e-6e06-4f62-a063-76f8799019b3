package com.facishare.fmcg.api.dto.custom;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.ResultBase;
import com.facishare.fmcg.api.error.FmcgException;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fs.fmcg.sdk.ai.contract.DetectResultDTO;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/13 14:59
 */
public interface ImageDetect {

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Arg extends ArgBase {

        @JSONField(name = "model_id")
        @JsonProperty(value = "model_id")
        @SerializedName("model_id")
        private String modelId;

        @JSONField(name = "path")
        @JsonProperty(value = "path")
        @SerializedName("path")
        private String path;

        @JSONField(name = "is_pure_detect")
        @JsonProperty(value = "is_pure_detect")
        @SerializedName("is_pure_detect")
        private Boolean isPureDetect;

        @JSONField(name = "extra_data")
        @JsonProperty(value = "extra_data")
        @SerializedName("extra_data")
        private String extraData;

        @Override
        public void insure() {

        }

        @Override
        public void validate() throws FmcgException {

        }
    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    @Builder
    class Result extends ResultBase {

        @JSONField(name = "total_count")
        @JsonProperty(value = "total_count")
        @SerializedName("total_count")
        private Integer totalCount;

        @JSONField(name = "data")
        @JsonProperty(value = "data")
        @SerializedName("data")
        private DetectResultDTO data;
    }
}
