package com.facishare.fmcg.api.service.custom;

import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.custom.DevicePhotoAppeal;
import com.facishare.fmcg.api.dto.custom.DevicePhotoDetailLocation;
import com.facishare.fmcg.api.dto.custom.DevicePhotoObject;
import com.facishare.fmcg.api.dto.custom.QueryDevicePhotoDetail;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/4/11 11:40
 */
public interface IDevicePhotoService {

    ApiResult<DevicePhotoObject.Result> addDevicePhoto(ApiArg<DevicePhotoObject.Arg> arg);

    ApiResult<QueryDevicePhotoDetail.Result> queryDevicePhotoDetail(ApiArg<QueryDevicePhotoDetail.Arg> arg);

    ApiResult<DevicePhotoAppeal.Result> appeal(ApiArg<DevicePhotoAppeal.Arg> arg);

    ApiResult<DevicePhotoDetailLocation.Result> getPhoto(ApiArg<DevicePhotoDetailLocation.Arg> arg);
}
