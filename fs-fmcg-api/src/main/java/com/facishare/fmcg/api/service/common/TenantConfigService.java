package com.facishare.fmcg.api.service.common;

import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.common.config.*;

/**
 * <AUTHOR>
 */
public interface TenantConfigService {

    ApiResult<Get.Result> get(ApiArg<Get.Arg> arg);

    ApiResult<Set.Result> set(ApiArg<Set.Arg> arg);

    ApiResult<Query.Result> query(ApiArg<Query.Arg> arg);

    /**
     * get ticket print able config
     *
     * @param arg empty arg
     * @return config value
     */
    ApiResult<GetTicketConfig.Result> getTicketConfig(ApiArg<GetTicketConfig.Arg> arg);


    ApiResult<ExistRecord.Result> existRecord(ApiArg<ExistRecord.Arg> arg);
}
