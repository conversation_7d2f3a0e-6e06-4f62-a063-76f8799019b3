package com.facishare.fmcg.adapter.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.facishare.fmcg.api.error.ErrorCode;
import com.facishare.fmcg.api.error.FmcgException;
import com.github.autoconf.admin.ConfigAdminClient;
import com.google.common.base.CharMatcher;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * author: wuyx
 * description:
 * createTime: 2022/3/21 12:02
 */
@Slf4j
@Component
public class ConfigService {

    @Resource
    private ConfigAdminClient configAdminClient;

    private final static String SEPARATORS = "=:";

    private String token = "62FF5D8DA743BD9054ABF597329C4F6B1FAFBD1D563F54F2A1133E2FC96A1B01";


    public Map<String, String> get(String configName) {
        String content = null;
        try {
            content = configAdminClient.get(token, configName);
        } catch (Exception e) {
            log.error("e:", e);
        }

        Map<String, String> configMap = Maps.newHashMap();
        if (StringUtils.isNotEmpty(content)) {
            String[] infos = content.split("\n");
            for (String info : infos) {
                if (info.trim().length() != 0 && !info.trim().startsWith("#")) {
                    int index = CharMatcher.anyOf(SEPARATORS).indexIn(info);
                    configMap.put(info.substring(0, index).trim(), info.substring(index + 1).trim());
                }
            }
        }
        return configMap;
    }


    public void update(String configName, String key, String value) {
        Map<String, String> configMap = get(configName);
        String property = getProperty();

        if (configMap.isEmpty()) {
            return;
        }
        if (!configMap.containsKey(key)) {
            throw new FmcgException(ErrorCode.CONFIG_KEY_NOT_FOUND.getMessage(), ErrorCode.CONFIG_KEY_NOT_FOUND.getCode());
        }
        boolean update = false;
        try {
            //这里更新某个配置需要间隔一秒，否则版本可能会覆盖 没更新上
            try {
                Thread.sleep(3000);
            } catch (InterruptedException ex) {
                log.error("update polling sleep error : ", ex);
            }
            update = configAdminClient.update(token, property, configName, key, value);
        } catch (Exception e) {
            log.error("更新配置文件 property ={},configName={},key={},value={} 失败", property, configName, key, value, e);
        }
        if (!update) {
            log.error("更新配置文件  property ={},configName={},key={},value={} 失败", property, configName, key, value);
        }
    }


    public void updateMulti(String configName, Map<String,String> updateMap) {
        Map<String, String> configMap = get(configName);
        String property = getProperty();

        if (configMap.isEmpty()) {
            return;
        }

        boolean update = false;
        try {
            //这里更新某个配置需要间隔一秒，否则版本可能会覆盖 没更新上
            try {
                Thread.sleep(3000);
            } catch (InterruptedException ex) {
                log.error("update polling sleep error : ", ex);
            }
            update = configAdminClient.updateMultiple(token, property, configName, updateMap);
        } catch (Exception e) {
            log.error("更新配置文件 property ={},configName={},value={} 失败", property, configName, updateMap, e);
        }
        if (!update) {
            log.error("更新配置文件  property ={},configName={},value={} 失败", property, configName, updateMap);
        }
    }

    private String getProperty() {
        String candidates = System.getProperty("process.profile.candidates");
        if (!Strings.isNullOrEmpty(candidates)) {
            String[] properties = candidates.split(",");
            return properties[0];
        } else {
            String env = System.getenv("ENVIRONMENT_TYPE");
            if ("firstshare".equals(env)) {
                return "fstest";
            } else if ("foneshare".equals(env)) {
                return "foneshare";
            } else if (!Strings.isNullOrEmpty(env)) {
                return "cloud";
            }
        }
        return System.getProperty("process.profile");
    }
}
