package com.facishare.fmcg.adapter.metadata.dto.data;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 19-12-17  下午3:50
 */
public interface RelatedList {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "associate_object_data_id")
        private String masterObjId;

        @JSONField(name = "associate_object_describe_api_name")
        private String masterApiName;

        @J<PERSON>NField(name = "associated_object_describe_api_name")
        private String slaveApiName;

        @J<PERSON>NField(name = "associated_object_field_related_list_name")
        private String relatedListName;

        @JSONField(name = "include_associated")
        private boolean includeAssociated = true;

        @JSONField(name = "search_query_info")
        private String queryString = "{\"limit\":2000,\"offset\":0}";

        @JSONField(name = "is_ordered")
        private boolean ordered = true;

    }

    @Data
    @ToString
    class Result implements Serializable {

        private Integer errCode;

        private String errMessage;

        private JSONObject result;
    }
}
