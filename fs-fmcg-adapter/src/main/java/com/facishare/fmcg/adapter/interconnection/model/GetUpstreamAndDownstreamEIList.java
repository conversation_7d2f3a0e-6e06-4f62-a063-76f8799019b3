package com.facishare.fmcg.adapter.interconnection.model;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/22 上午11:15
 */
public interface GetUpstreamAndDownstreamEIList {

    @Data
    @ToString
    class Arg{
        private String ea;
    }

    @Data
    @ToString
    class Result{
        private List<Integer> upstreamIds;
        private List<Integer> downstreamIds;
    }
}
