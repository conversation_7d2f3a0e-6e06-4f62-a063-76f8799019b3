package com.facishare.fmcg.adapter.organization;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.fmcg.adapter.checkins.CheckInsAdapterImpl;
import com.facishare.fmcg.api.dto.common.organization.EmployeeDTO;
import com.facishare.organization.adapter.api.model.biz.department.Department;
import com.facishare.organization.adapter.api.model.biz.department.arg.GetChildrenDepartmentArg;
import com.facishare.organization.adapter.api.model.biz.department.arg.GetDepartmentArg;
import com.facishare.organization.adapter.api.model.biz.department.result.GetDepartmentResult;
import com.facishare.organization.adapter.api.model.biz.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.adapter.api.model.biz.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.adapter.api.service.DepartmentService;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.BatchGetLowDepartmentIds;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.*;
import com.facishare.organization.api.model.department.result.*;
import com.facishare.organization.api.model.departmentmember.MainDepartment;
import com.facishare.organization.api.model.employee.BatchGetEmployeeIdMapByDepartmentId;
import com.facishare.organization.api.model.employee.BatchGetEmployeeNameMapByDepartmentId;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.EmployeeName;
import com.facishare.organization.api.model.employee.arg.*;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeesDtoByDepartmentIdResult;
import com.facishare.organization.api.model.employee.result.GetAllSubordinateEmployeesDtoResult;
import com.facishare.organization.api.model.employee.result.GetSubordinateEmployeesDtoResult;
import com.facishare.organization.api.model.type.DepartmentStatus;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.organization.paas.model.permission.QueryUserByRoleDto;
import com.facishare.organization.paas.service.PaaSPermissionService;
import com.facishare.organization.paas.util.PaasArgumentUtil;
import com.facishare.social.department.DepartmentObjService;
import com.facishare.social.department.model.GetDepartmentByAssistantId;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@SuppressWarnings("Duplicates")
public class OrganizationAdapterImpl implements OrganizationAdapter {

    @Resource
    private EmployeeService employeeService;

    @Resource
    private DepartmentService departmentService;

    @Resource
    private EmployeeProviderService employeeProviderService;

    @Resource
    private DepartmentProviderService departmentProviderService;

    @Autowired
    private DepartmentObjService paasDepartmentObjService;

    @Resource
    private PaaSPermissionService paaSPermissionService;

    @Resource
    private CheckInsAdapterImpl checkinsAdapter;

    @Resource
    private EIEAConverter eieaConverter;

    private static final String ROLE_APP_ID = "CRM";

    private static final String UNION = "union";

    private static final String NORMAL = "normal";

    private static final Logger logger = LoggerFactory.getLogger(OrganizationAdapterImpl.class);

    @Override
    public List<EmployeeDto> queryEmployees(int ei, List<Integer> employeeIds) {
        BatchGetEmployeeDtoArg arg = new BatchGetEmployeeDtoArg();
        arg.setEnterpriseId(ei);
        arg.setEmployeeIds(employeeIds);
        arg.setRunStatus(com.facishare.organization.adapter.api.model.biz.RunStatus.fromAPI(RunStatus.ALL));
        BatchGetEmployeeDtoResult rst = employeeService.batchGetEmployeeDto(arg);
        return rst.getEmployees();
    }

    @Override
    public List<DepartmentDto> queryDepartments(int ei, List<Integer> departmentIds) {
        BatchGetDepartmentDtoArg arg = new BatchGetDepartmentDtoArg();
        arg.setEnterpriseId(ei);
        arg.setDepartmentIds(departmentIds);
        arg.setRunStatus(com.facishare.organization.api.model.RunStatus.ALL);
        BatchGetDepartmentDtoResult result = departmentProviderService.batchGetDepartmentDto(arg);

        return result.getDepartments();
    }

    @Override
    public List<Integer> batchGetLowDepartmentIds(int ei, List<Integer> departmentIds) {
        BatchGetLowDepartmentIds.Arg arg = new BatchGetLowDepartmentIds.Arg();
        arg.setDepartmentIds(departmentIds);
        arg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);
        arg.setSelf(true);
        arg.setRecursive(true);
        arg.setEnterpriseId(ei);
        return departmentProviderService.batchGetLowDepartmentIds(arg).getDepartmentIds();
    }

    @Override
    public EmployeeDto getEmployee(int ei, int employeeId) {
        List<Integer> employeeIds = new ArrayList<>();
        employeeIds.add(employeeId);
        return queryEmployees(ei, employeeIds).stream().findFirst().orElse(null);
    }

    @Override
    public Department getDepartment(int ei, int departmentId) {
        GetDepartmentArg getDepartmentArg = new GetDepartmentArg();
        getDepartmentArg.setCurrentEmployeeId(1000);
        getDepartmentArg.setDepartmentId(departmentId);
        getDepartmentArg.setEnterpriseId(ei);
        return departmentService.getDepartment(getDepartmentArg).getDepartment();
    }

    public Department getDepartmentByEmployeeId(int ei, int employeeId) {
        EmployeeDto employeeDto = getEmployee(ei, employeeId);

        GetDepartmentArg getDepartmentArg = new GetDepartmentArg();
        getDepartmentArg.setCurrentEmployeeId(employeeId);
        getDepartmentArg.setDepartmentId(employeeDto.getMainDepartmentId());
        getDepartmentArg.setEnterpriseId(ei);
        GetDepartmentResult result = departmentService.getDepartment(getDepartmentArg);
        return result.getDepartment();
    }

    @Override
    public List<DepartmentDto> getDepartmentsByIUserIds(int ei, int employeeId) {
        List<DepartmentDto> result = Lists.newArrayList();
        BatchGetDepartmentByPrincipalArg arg = new BatchGetDepartmentByPrincipalArg();
        arg.setEnterpriseId(ei);
        arg.setPrincipalIds(Lists.newArrayList(employeeId));
        arg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);
        BatchGetDepartmentByPrincipalResult queryResult = departmentProviderService.batchGetDepartmentByPrincipal(arg);
        if (queryResult != null && CollectionUtils.isNotEmpty(queryResult.getDepartments())) {
            result.addAll(queryResult.getDepartments());
        }

        GetEmployeeDtoArg getEmployeeDtoArg = new GetEmployeeDtoArg();
        getEmployeeDtoArg.setEmployeeId(employeeId);
        getEmployeeDtoArg.setEnterpriseId(ei);

        EmployeeDto employeeDto = employeeProviderService.getEmployeeDto(getEmployeeDtoArg).getEmployeeDto();
        if (!checkinsAdapter.checkIsAppAdminDubbo(eieaConverter.enterpriseIdToAccount(ei), employeeId)) {
            if (employeeDto.getMainDepartmentId() == null) {
                return result;
            }
            GetLowDepartmentsDtoArg departmentsDtoArg = new GetLowDepartmentsDtoArg(employeeDto.getMainDepartmentId(), false, RunStatus.ACTIVE);
            departmentsDtoArg.setEnterpriseId(ei);
            GetLowDepartmentsDtoResult getLowDepartmentsDtoResult = departmentProviderService.getLowDepartmentsDto(departmentsDtoArg);
            if (getLowDepartmentsDtoResult != null && CollectionUtils.isNotEmpty(getLowDepartmentsDtoResult.getDepartmentDtos())) {
                result.addAll(getLowDepartmentsDtoResult.getDepartmentDtos());
            }
            final GetDepartmentByAssistantId.Argument argument = new GetDepartmentByAssistantId.Argument();
            argument.setAssistantId(String.valueOf(employeeId));
            argument.setDownRecursive(true);
            argument.setTenantId(String.valueOf(ei));
            argument.setOperatorId("-10000");

            final List<com.facishare.social.department.model.DepartmentDto> departmentList = paasDepartmentObjService.getDepartmentByAssistantId(argument);
            for (com.facishare.social.department.model.DepartmentDto departmentDto : departmentList) {
                DepartmentDto dto = new DepartmentDto();
                dto.setDepartmentId(Integer.parseInt(departmentDto.getDeptId()));
                dto.setName(departmentDto.getName());
                dto.setStatus(DepartmentStatus.NORMAL);

                result.add(dto);
            }
        } else {
            GetAllDepartmentDtoArg allDepartmentDtoArg = new GetAllDepartmentDtoArg();
            allDepartmentDtoArg.setRunStatus(RunStatus.ACTIVE);
            allDepartmentDtoArg.setEnterpriseId(ei);

            GetAllDepartmentDtoResult getAllDepartmentDtoResult = departmentProviderService.getAllDepartmentDto(allDepartmentDtoArg);
            if (CollectionUtils.isNotEmpty(getAllDepartmentDtoResult.getDepartments())) {
                result.addAll(getAllDepartmentDtoResult.getDepartments());
            }
        }

        return result;
    }

    @Override
    public List<EmployeeDTO> querySubordinateEmployees(int ei, int employeeId) {
        List<EmployeeDTO> employees = Lists.newArrayList();
        GetSubordinateEmployeesDtoArg getAllSubordinateEmployeesDtoArg = new GetSubordinateEmployeesDtoArg();
        getAllSubordinateEmployeesDtoArg.setEnterpriseId(ei);
        getAllSubordinateEmployeesDtoArg.setEmployeeId(employeeId);
        getAllSubordinateEmployeesDtoArg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);

        GetSubordinateEmployeesDtoResult result = employeeProviderService.getSubordinateEmployees(getAllSubordinateEmployeesDtoArg);

        result.getEmployeeDtos().stream().filter(f -> f.getStatus().is(EmployeeEntityStatus.NORMAL)).forEach(employeeDto -> {
            EmployeeDTO employee = new EmployeeDTO();
            employee.setId(employeeDto.getEmployeeId());
            employee.setDisplayName(employeeDto.getName());
            employee.setDepartmentId(employeeDto.getMainDepartmentId());
            employees.add(employee);
        });

        return employees;
    }

    @Override
    public List<EmployeeDTO> queryAllSubordinateEmployees(int ei, int employeeId) {
        List<EmployeeDTO> employees = Lists.newArrayList();
        GetAllSubordinateEmployeesDtoArg getAllSubordinateEmployeesDtoArg = new GetAllSubordinateEmployeesDtoArg();
        getAllSubordinateEmployeesDtoArg.setEnterpriseId(ei);
        getAllSubordinateEmployeesDtoArg.setEmployeeId(employeeId);
        getAllSubordinateEmployeesDtoArg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);

        GetAllSubordinateEmployeesDtoResult result = employeeProviderService.getAllSubordinateEmployees(getAllSubordinateEmployeesDtoArg);

        result.getEmployeeDtos().stream().filter(f -> f.getStatus().is(EmployeeEntityStatus.NORMAL)).forEach(employeeDto -> {
            EmployeeDTO employee = new EmployeeDTO();
            employee.setId(employeeDto.getEmployeeId());
            employee.setDisplayName(employeeDto.getName());
            employee.setDepartmentId(employeeDto.getMainDepartmentId());
            employees.add(employee);
        });

        return employees;
    }

    @Override
    public List<EmployeeDTO> queryAllColleagues(int ei, int employeeId) {

        Department department = getDepartmentByEmployeeId(ei, employeeId);

        GetChildrenDepartmentArg getChildrenDepartmentArg = new GetChildrenDepartmentArg();
        getChildrenDepartmentArg.setEnterpriseId(ei);
        getChildrenDepartmentArg.setDepartmentId(department.getParentDepartmentId());
        getChildrenDepartmentArg.setSelf(false);
        getChildrenDepartmentArg.setCurrentEmployeeId(employeeId);
        List<Department> departments = departmentService.getChildrenDepartment(getChildrenDepartmentArg).getDepartments();

        BatchGetEmployeeMapByDepartmentIdArg arg = new BatchGetEmployeeMapByDepartmentIdArg();
        arg.setDepartmentIds(departments.stream().map(Department::getDepartmentId).collect(Collectors.toList()));
        arg.setEnterpriseId(ei);
        arg.setIncludeLowDepartment(false);
        arg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);
        arg.setMainDepartment(MainDepartment.MAIN);
        Map<Integer, List<EmployeeDto>> employeeMap = employeeProviderService.batchGetEmployeeMapByDepartmentId(arg).getEmployeeMap();

        List<EmployeeDTO> employees = Lists.newArrayList();
        employeeMap.values().stream().flatMap(Collection::stream).filter(f -> f.getStatus().is(EmployeeEntityStatus.NORMAL)).forEach(employeeDto -> {
            EmployeeDTO employee = new EmployeeDTO();
            employee.setId(employeeDto.getEmployeeId());
            employee.setDisplayName(employeeDto.getName());
            employee.setDepartmentId(employeeDto.getMainDepartmentId());
            if (employeeDto.getEmployeeId() == employeeId) {
                employees.add(0, employee);
            } else {
                employees.add(employee);
            }
        });

        return employees;
    }

    @Override
    public List<EmployeeDTO> queryAllEmployeeIds(int ei, int employeeId, List<Integer> employeeIds, List<Integer> departmentIds, List<String> roles, String type) {
        if (StringUtils.isBlank(type)) {
            type = NORMAL;
        }

        List<Integer> employeeList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(employeeIds)) {
            employeeList.addAll(employeeIds);
        }
        List<Integer> departmentEmployeeIds = queryEmployeeIdsByDepartmentIds(ei, departmentIds);
        List<Integer> roleEmployeeIds = queryEmployeeIdsByRoleCodes(ei, roles);
        employeeList.addAll(departmentEmployeeIds);

        //根绝type判断取交集还是并集
        if (UNION.equals(type)) {
            employeeList.addAll(roleEmployeeIds);
        } else if (NORMAL.equals(type)) {
            employeeList = employeeList.stream().filter(roleEmployeeIds::contains).collect(Collectors.toList());
        }
        //当查询员工包含自己时，将自己放到第一个位置
        if (employeeList.contains(employeeId)) {
            employeeList.add(0, employeeId);
        }
        List<Integer> allEmployeeIds = employeeList.stream().distinct().collect(Collectors.toList());


        return queryEmployeesByIds(ei, employeeId, allEmployeeIds);
    }

    @Override
    public Map<Integer, List<EmployeeDTO>> queryDepartmentIdToEmployeeIds(int ei, List<Integer> departmentIds) {
        BatchGetEmployeeNameMapByDepartmentId.Arg arg = new BatchGetEmployeeNameMapByDepartmentId.Arg();
        arg.setDepartmentIds(departmentIds);
        arg.setEnterpriseId(ei);
        arg.setIncludeLowDepartment(false);
        arg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);
        arg.setMainDepartment(MainDepartment.MAIN);

        Map<Integer, List<EmployeeName>> departmentEmployeesMap = employeeProviderService.batchGetEmployeeNameMapByDepartmentId(arg).getEmployeeMap();
        Map<Integer, List<EmployeeDTO>> result = Maps.newHashMap();
        departmentEmployeesMap.forEach((departmentId, employeeNames) -> {
            List<EmployeeDTO> list = Lists.newArrayList();
            employeeNames.forEach(employeeName -> {
                EmployeeDTO employee = new EmployeeDTO();
                employee.setId(employeeName.getEmployeeId());
                employee.setDisplayName(employeeName.getName());
                list.add(employee);
            });
            result.put(departmentId, list);
        });

        return result;
    }

    @Override
    public List<EmployeeDTO> queryAllEmployeesByDepartmentPrincipal(int tenantId, int employeeId) {
        List<EmployeeDTO> ans = new LinkedList<>();
        BatchGetDepartmentByPrincipalArg arg = new BatchGetDepartmentByPrincipalArg();
        arg.setPrincipalIds(Lists.newArrayList(employeeId));
        arg.setEnterpriseId(tenantId);
        arg.setRunStatus(RunStatus.ACTIVE);
        BatchGetDepartmentByPrincipalResult departmentByPrincipal = departmentProviderService.batchGetDepartmentByPrincipal(arg);
        if (CollectionUtils.isEmpty(departmentByPrincipal.getDepartments())) {
            return ans;
        }
        List<Integer> departmentIds = departmentByPrincipal.getDepartments().stream()
                .map(DepartmentDto::getDepartmentId)
                .collect(Collectors.toList());

        BatchGetLowDepartmentsDtoArg queryDepartmentArg = new BatchGetLowDepartmentsDtoArg();
        queryDepartmentArg.setDepartmentIds(departmentIds);
        queryDepartmentArg.setSelf(false);
        queryDepartmentArg.setEnterpriseId(tenantId);
        BatchGetLowDepartmentsDtoResult subordinateDepartment = departmentProviderService.batchGetLowDepartment(queryDepartmentArg);
        List<Integer> subordinateDepartmentIds = subordinateDepartment.getDepartmentDtos().stream()
                .filter(o -> o.getStatus().is(DepartmentStatus.NORMAL))
                .map(DepartmentDto::getDepartmentId)
                .collect(Collectors.toList());
        departmentIds.addAll(subordinateDepartmentIds);

        BatchGetEmployeesDtoByDepartmentIdArg queryEmployeesArg = new BatchGetEmployeesDtoByDepartmentIdArg();
        queryEmployeesArg.setDepartmentIds(departmentIds);
        queryEmployeesArg.setEnterpriseId(tenantId);
        queryEmployeesArg.setIncludeLowDepartment(false);
        queryEmployeesArg.setRunStatus(RunStatus.ACTIVE);
        BatchGetEmployeesDtoByDepartmentIdResult employees = employeeProviderService.batchGetEmployeesByDepartmentId(queryEmployeesArg);

        employees.getEmployeeDtos().stream()
                .filter(f -> f.getStatus().is(EmployeeEntityStatus.NORMAL) && f.getEmployeeId() != employeeId)
                .forEach(employeeDto -> {
                    EmployeeDTO employee = new EmployeeDTO();
                    employee.setId(employeeDto.getEmployeeId());
                    employee.setDisplayName(employeeDto.getName());
                    employee.setDepartmentId(employeeDto.getMainDepartmentId());
                    ans.add(employee);
                });
        return ans;
    }

    public List<EmployeeDTO> queryEmployeesByIds(int ei, int employeeId, List<Integer> employeeList) {
        List<EmployeeDTO> employees = Lists.newArrayList();
        if (CollectionUtils.isEmpty(employeeList)) {
            return employees;
        }

        BatchGetEmployeeDtoArg arg = new BatchGetEmployeeDtoArg();
        arg.setEnterpriseId(ei);
        arg.setEmployeeIds(employeeList);
        arg.setRunStatus(com.facishare.organization.adapter.api.model.biz.RunStatus.fromAPI(RunStatus.ACTIVE));
        BatchGetEmployeeDtoResult rst = employeeService.batchGetEmployeeDto(arg);

        rst.getEmployees().forEach(f -> {
            EmployeeDTO employee = new EmployeeDTO();
            employee.setId(f.getEmployeeId());
            employee.setDisplayName(f.getName());
            if (f.getEmployeeId() == employeeId) {
                employees.add(0, employee);
            } else {
                employees.add(employee);
            }
        });

        return employees;
    }

    public List<Integer> queryEmployeeIdsByRoleCodes(int ei, List<String> roles) {
        List<Integer> roleEmployeeIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(roles)) {
            return roleEmployeeIds;
        }

        QueryUserByRoleDto.Argument roleArgument = PaasArgumentUtil.buildPaaSPermissionArgument(QueryUserByRoleDto.Argument.class, ei, -10000, ROLE_APP_ID);
        roleArgument.setRoles(roles);

        Map<String, List<String>> roleEmployeeList = paaSPermissionService.queryUserByRole(roleArgument).getResult();

        logger.info("query employeeIds by role codes result : {}", JSON.toJSONString(roleEmployeeList));

        roleEmployeeList.values().forEach(f -> f.forEach(roleEmployeeId -> roleEmployeeIds.add(Integer.valueOf(roleEmployeeId))));

        return roleEmployeeIds;

    }

    public List<EmployeeDTO> queryEmployeesByDepartmentIds(int ei, List<Integer> departmentIds, boolean flag) {
        List<EmployeeDTO> employees = Lists.newArrayList();

        if (CollectionUtils.isEmpty(departmentIds)) {
            return employees;
        }
        BatchGetEmployeesDtoByDepartmentIdArg arg = new BatchGetEmployeesDtoByDepartmentIdArg();
        arg.setDepartmentIds(departmentIds);
        arg.setEnterpriseId(ei);
        arg.setIncludeLowDepartment(flag);
        arg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);
        arg.setMainDepartment(MainDepartment.ALL);

        List<EmployeeDto> employeeDtos = employeeProviderService.batchGetEmployeesByDepartmentId(arg).getEmployeeDtos();
        employeeDtos.forEach(f -> {
            EmployeeDTO employeeDTO = new EmployeeDTO();
            employeeDTO.setId(f.getEmployeeId());
            employeeDTO.setDisplayName(f.getName());
            employeeDTO.setDepartmentId(f.getMainDepartmentId());
            employees.add(employeeDTO);
        });

        return employees;
    }

    public List<Integer> queryEmployeeIdsByDepartmentIds(int ei, List<Integer> departmentIds) {
        List<Integer> departmentEmployeeIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(departmentIds)) {
            return departmentEmployeeIds;
        }
        BatchGetEmployeeIdMapByDepartmentId.Arg arg = new BatchGetEmployeeIdMapByDepartmentId.Arg();
        arg.setDepartmentIds(departmentIds);
        arg.setEnterpriseId(ei);
        arg.setIncludeLowDepartment(false);
        arg.setRunStatus(com.facishare.organization.api.model.RunStatus.ALL);
        arg.setMainDepartment(MainDepartment.MAIN);

        Map<Integer, List<Integer>> departmentEmployeeList = employeeProviderService.batchGetEmployeeIdMapByDepartmentId(arg).getEmployeeMap();

        logger.info("query employeeIds by role department ids result : {}", JSON.toJSONString(departmentEmployeeList));

        departmentEmployeeList.values().forEach(departmentEmployeeIds::addAll);

        return departmentEmployeeIds;
    }

}
