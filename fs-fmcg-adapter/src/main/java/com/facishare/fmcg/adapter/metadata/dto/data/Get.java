package com.facishare.fmcg.adapter.metadata.dto.data;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface Get {

    @Data
    @ToString
    class Result implements Serializable {

        private Integer code;

        private String message;

        private DataDto data;
    }

    @Data
    @ToString
    class DataDto implements Serializable {

        @JSONField(name = "object_data")
        private JSONObject objectData;
    }
}
