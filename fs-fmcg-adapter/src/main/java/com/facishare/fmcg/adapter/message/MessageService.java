package com.facishare.fmcg.adapter.message;


import com.facishare.fmcg.adapter.message.model.SessionContent;

import java.util.List;

public interface MessageService {

    void sendLimitedNotifyAsync(String ea, List<Integer> receivers,String category,String title, String subTitle,String message);

    void sendAsync(String ea, Integer receiver, String subTitle, String message, String url);

    void sendSessionForExport(List<Integer> receivers, String ea, com.facishare.qixin.api.model.AuthInfo auth,
                              String clientVersion, SessionContent sessionContent);

    void sendTextMessage(String message, String ea, List<Integer> employeeIds);
}
