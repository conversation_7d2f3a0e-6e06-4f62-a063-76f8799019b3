package com.facishare.fmcg.adapter.metadata;

import com.facishare.fmcg.adapter.metadata.dto.data.*;
import com.facishare.fmcg.adapter.metadata.dto.describe.Edit;

/**
 * <AUTHOR>
 */
public interface DataAdapter {

    Edit.Result edit(int tenantId, int employeeId, String apiName, Edit.Arg arg);

    Get.Result get(int tenantId, String apiName, String dataId);

    Query.Result query(int tenantId, Query.Arg arg);

    RelatedList.Result relatedList(int tenantId, int employeeId, String apiName, RelatedList.Arg arg);

    QueryLayout.Result queryLayout(int tenantId, QueryLayout.Arg arg);

    CreateData.Result create(int tenantId, int employeeId, String apiName, CreateData.Arg arg);

    IncrementUpdate.Result incrementUpdate(int tenantId,int employeeId,String apiName,IncrementUpdate.Arg arg);
}
